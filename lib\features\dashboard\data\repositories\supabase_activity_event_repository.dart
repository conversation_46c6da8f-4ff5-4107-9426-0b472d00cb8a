import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/activity_event.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/repositories/activity_event_repository.dart';
import 'package:logger/logger.dart';

class SupabaseActivityEventRepository implements ActivityEventRepository {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  SupabaseActivityEventRepository(this._supabaseService);

  @override
  Future<List<ActivityEvent>> getRecentActivityEvents({
    String? roleId,
    String? sppgId,
    int limit = 50,
  }) async {
    _logger.i(
      'Getting recent activity events for role: $roleId, sppgId: $sppgId, limit: $limit',
    );

    try {
      var query = _supabaseService.client.from('activity_events').select();

      // Apply filters using the match method
      Map<String, dynamic> matchCriteria = {};

      if (roleId != null) {
        matchCriteria['visible_to_roles'] = roleId;
      }

      if (sppgId != null) {
        matchCriteria['sppg_id'] = sppgId;
      }

      if (matchCriteria.isNotEmpty) {
        query = query.match(matchCriteria);
      }

      // Apply ordering and limit
      final response = await query
          .order('timestamp', ascending: false)
          .limit(limit);

      _logger.d('Activity events retrieved: ${response.length} events');

      return response.map((item) => _parseActivityEvent(item)).toList();
    } on Exception catch (e) {
      _logger.e('Failed to get activity events: $e');

      // Return sample data if response is not a list
      return _getSampleActivityEvents();
    } catch (e, stackTrace) {
      _logger.e('Failed to get activity events: $e', stackTrace: stackTrace);

      // Return sample data on error
      return _getSampleActivityEvents();
    }
  }

  @override
  Stream<ActivityEvent> watchActivityEvents({String? roleId, String? sppgId}) {
    _logger.i('Watching activity events for role: $roleId, sppgId: $sppgId');

    try {
      final stream = _supabaseService.client
          .from('activity_events')
          .stream(primaryKey: ['id']);

      // Filter the stream in-memory since Supabase stream API has different filtering capabilities
      return stream.map((event) => _parseActivityEvent(event)).where((event) {
        // Apply filters in memory
        bool matchesRole =
            roleId == null || event.visibleToRoles.contains(roleId);
        bool matchesSppg = sppgId == null || event.sppgId == sppgId;
        return matchesRole && matchesSppg;
      });
    } catch (e, stackTrace) {
      _logger.e('Failed to watch activity events: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Stream<ActivityEvent> watchNewActivityEvents({
    String? roleId,
    String? sppgId,
    DateTime? since,
  }) {
    _logger.i(
      'Watching new activity events for role: $roleId, sppgId: $sppgId, since: $since',
    );

    try {
      final stream = _supabaseService.client
          .from('activity_events')
          .stream(primaryKey: ['id']);

      // Filter the stream in-memory
      return stream.map((event) => _parseActivityEvent(event)).where((event) {
        // Apply filters in memory
        bool matchesRole =
            roleId == null || event.visibleToRoles.contains(roleId);
        bool matchesSppg = sppgId == null || event.sppgId == sppgId;
        bool matchesTime = since == null || event.timestamp.isAfter(since);
        return matchesRole && matchesSppg && matchesTime;
      });
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to watch new activity events: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> createActivityEvent(ActivityEvent event) async {
    _logger.i('Creating activity event: ${event.title}');

    try {
      await _supabaseService.client.from('activity_events').insert({
        'id': event.id,
        'title': event.title,
        'description': event.description,
        'type': event.type.name,
        'sppg_id': event.sppgId,
        'sppg_name': event.sppgName,
        'timestamp': event.timestamp.toIso8601String(),
        'severity': event.severity.name,
        'data': event.data,
        'visible_to_roles': event.visibleToRoles,
      });

      _logger.i('Activity event created successfully');
    } catch (e, stackTrace) {
      _logger.e('Failed to create activity event: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  // Parse activity event from response
  ActivityEvent _parseActivityEvent(Map<String, dynamic> data) {
    return ActivityEvent(
      id: data['id'] as String? ?? '',
      title: data['title'] as String? ?? '',
      description: data['description'] as String? ?? '',
      type: _parseActivityType(data['type'] as String?),
      sppgId: data['sppg_id'] as String?,
      sppgName: data['sppg_name'] as String? ?? '',
      timestamp:
          data['timestamp'] != null
              ? DateTime.parse(data['timestamp'] as String)
              : DateTime.now(),
      severity: _parseActivitySeverity(data['severity'] as String?),
      data: data['data'] as Map<String, dynamic>? ?? {},
      visibleToRoles:
          data['visible_to_roles'] is List
              ? List<String>.from(data['visible_to_roles'] as List)
              : ['admin_yayasan'],
    );
  }

  // Parse activity type from string
  ActivityType _parseActivityType(String? type) {
    switch (type?.toLowerCase()) {
      case 'delivery_completed':
        return ActivityType.deliveryCompleted;
      case 'qc_check_passed':
        return ActivityType.qcCheckPassed;
      case 'report_generated':
        return ActivityType.reportGenerated;
      case 'approval_requested':
        return ActivityType.approvalRequested;
      case 'production_completed':
        return ActivityType.productionCompleted;
      case 'order_completed':
        return ActivityType.orderCompleted;
      case 'notification':
        return ActivityType.notification;
      default:
        return ActivityType.notification;
    }
  }

  // Parse activity severity from string
  ActivitySeverity _parseActivitySeverity(String? severity) {
    switch (severity?.toLowerCase()) {
      case 'critical':
        return ActivitySeverity.critical;
      case 'warning':
        return ActivitySeverity.warning;
      case 'info':
        return ActivitySeverity.info;
      default:
        return ActivitySeverity.info;
    }
  }

  // Get sample activity events
  List<ActivityEvent> _getSampleActivityEvents() {
    return [
      ActivityEvent(
        id: '1',
        title: 'Pengiriman Selesai',
        description: '500 porsi makanan telah dikirim ke SDN 01 Menteng',
        type: ActivityType.deliveryCompleted,
        sppgId: 'sppg-001',
        sppgName: 'SPPG Menteng',
        timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        severity: ActivitySeverity.info,
        data: {'portions': 500, 'school': 'SDN 01 Menteng', 'driver': 'Agus'},
        visibleToRoles: ['admin_yayasan', 'kepala_dapur'],
      ),
      ActivityEvent(
        id: '2',
        title: 'Quality Check Selesai',
        description: 'Quality check untuk menu hari ini telah lulus',
        type: ActivityType.qcCheckPassed,
        sppgId: 'sppg-002',
        sppgName: 'SPPG Kemayoran',
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        severity: ActivitySeverity.info,
        data: {'menu': 'Menu Senin', 'inspector': 'Budi', 'score': 95},
        visibleToRoles: ['admin_yayasan', 'kepala_dapur', 'ahli_gizi'],
      ),
      ActivityEvent(
        id: '3',
        title: 'Laporan Mingguan Dibuat',
        description: 'Laporan mingguan untuk SPPG Cengkareng telah dibuat',
        type: ActivityType.reportGenerated,
        sppgId: 'sppg-003',
        sppgName: 'SPPG Cengkareng',
        timestamp: DateTime.now().subtract(const Duration(days: 1)),
        severity: ActivitySeverity.info,
        data: {
          'report_type': 'Laporan Mingguan',
          'period': 'Minggu ke-3 Juli',
          'author': 'Sistem',
        },
        visibleToRoles: ['admin_yayasan', 'akuntan'],
      ),
      ActivityEvent(
        id: '4',
        title: 'Persetujuan Diperlukan',
        description: 'Menu minggu depan memerlukan persetujuan',
        type: ActivityType.approvalRequested,
        sppgId: 'sppg-001',
        sppgName: 'SPPG Menteng',
        timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        severity: ActivitySeverity.warning,
        data: {
          'item_type': 'Menu Mingguan',
          'requested_by': 'Dewi',
          'due_date':
              DateTime.now().add(const Duration(days: 1)).toIso8601String(),
        },
        visibleToRoles: ['admin_yayasan', 'ahli_gizi'],
      ),
      ActivityEvent(
        id: '5',
        title: 'Produksi Selesai',
        description: 'Produksi makanan untuk hari ini telah selesai',
        type: ActivityType.productionCompleted,
        sppgId: 'sppg-005',
        sppgName: 'SPPG Kelapa Gading',
        timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        severity: ActivitySeverity.info,
        data: {
          'menu': 'Menu Senin',
          'portions': 1200,
          'completion_time': '06:30',
        },
        visibleToRoles: ['admin_yayasan', 'kepala_dapur'],
      ),
    ];
  }
}
