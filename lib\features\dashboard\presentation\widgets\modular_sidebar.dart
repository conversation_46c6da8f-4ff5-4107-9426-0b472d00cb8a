import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../core/auth/domain/simplified_app_user.dart';
import '../../../../core/auth/presentation/auth_service.dart';
import '../../domain/entities/dashboard_configuration.dart';
import '../../domain/services/dashboard_permission_service.dart';
import '../cubit/navigation_cubit.dart';

/// Modular sidebar widget for dashboard navigation
class ModularSidebar extends StatefulWidget {
  /// Navigation configuration
  final NavigationConfiguration? configuration;

  /// Whether sidebar is collapsible
  final bool isCollapsible;

  /// Whether sidebar is collapsed by default
  final bool defaultCollapsed;

  /// Width of expanded sidebar
  final double expandedWidth;

  /// Width of collapsed sidebar
  final double collapsedWidth;

  /// Callback when sidebar is toggled
  final VoidCallback? onToggle;

  const ModularSidebar({
    super.key,
    this.configuration,
    this.isCollapsible = true,
    this.defaultCollapsed = false,
    this.expandedWidth = 240.0,
    this.collapsedWidth = 56.0,
    this.onToggle,
  });

  @override
  State<ModularSidebar> createState() => _ModularSidebarState();
}

class _ModularSidebarState extends State<ModularSidebar> {
  final Logger _logger = Logger();
  late NavigationCubit _navigationCubit;

  @override
  void initState() {
    super.initState();

    _navigationCubit = context.read<NavigationCubit>();

    // Initialize navigation cubit with configuration
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.configuration != null) {
        _navigationCubit.initializeNavigation(widget.configuration!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NavigationCubit, NavigationState>(
      builder: (context, state) {
        // If not initialized and configuration provided, use configuration
        if (!state.initialized && widget.configuration != null) {
          return _buildSidebar(
            widget.configuration!.sections,
            widget.isCollapsible,
            widget.defaultCollapsed,
            widget.expandedWidth,
            widget.collapsedWidth,
          );
        }

        // Otherwise use state from cubit
        return _buildSidebar(
          state.sections,
          state.isCollapsible,
          state.isCollapsed,
          state.expandedWidth,
          state.collapsedWidth,
        );
      },
    );
  }

  Widget _buildSidebar(
    List<NavigationSection> sections,
    bool isCollapsible,
    bool isCollapsed,
    double expandedWidth,
    double collapsedWidth,
  ) {
    final currentUser = AuthService.instance.currentUser;
    if (currentUser == null) {
      return const SizedBox.shrink();
    }

    // Filter sections based on user permissions
    final accessibleSections = DashboardPermissionService.getAccessibleSections(
      currentUser,
      sections,
    );

    return NavigationView(
      pane: NavigationPane(
        selected: _getSelectedIndex(accessibleSections),
        onChanged: (index) => _handleNavigation(accessibleSections, index),
        displayMode:
            isCollapsed ? PaneDisplayMode.compact : PaneDisplayMode.open,
        items: _buildNavigationItems(accessibleSections, currentUser),
        footerItems: _buildFooterItems(currentUser),
        header: _buildHeader(currentUser, isCollapsed),
      ),
    );
  }

  int _getSelectedIndex(List<NavigationSection> sections) {
    final activeRoute = context.read<NavigationCubit>().state.activeRoute;

    // Find matching item in sections
    int index = 0;
    for (final section in sections) {
      for (final item in section.items) {
        if (item.route == activeRoute) {
          return index;
        }
        index++;
      }
    }

    return 0; // Default to first item
  }

  void _handleNavigation(List<NavigationSection> sections, int index) {
    int itemCount = 0;

    // Find the selected item
    for (final section in sections) {
      for (final item in section.items) {
        if (itemCount == index) {
          _logger.i('Navigating to ${item.route}');

          // Update active route in cubit
          _navigationCubit.setActiveRoute(item.route);
          _navigationCubit.addToHistory(item.route);

          // Navigate to route
          context.go(item.route);
          return;
        }
        itemCount++;
      }
    }
  }

  List<NavigationPaneItem> _buildNavigationItems(
    List<NavigationSection> sections,
    AppUser user,
  ) {
    final items = <NavigationPaneItem>[];

    // Add items from each section
    for (int sectionIndex = 0; sectionIndex < sections.length; sectionIndex++) {
      final section = sections[sectionIndex];

      // Add section header
      items.add(PaneItemHeader(header: Text(section.title)));

      // Add section items
      for (final item in section.items) {
        // Skip items user doesn't have permission for
        if (!DashboardPermissionService.hasNavigationItemPermission(
          user,
          item,
        )) {
          continue;
        }

        // Get badge count if any
        final badgeCount = _navigationCubit.state.badgeCounts[item.route];

        items.add(
          PaneItem(
            icon: Icon(item.icon ?? FluentIcons.document),
            title: Text(item.title),
            body: const SizedBox.shrink(), // Required body parameter
            infoBadge:
                badgeCount != null && badgeCount > 0
                    ? InfoBadge(
                      source: Text('$badgeCount'),
                      color: item.badgeColor ?? Colors.red,
                    )
                    : null,
          ),
        );
      }
    }

    return items;
  }

  List<NavigationPaneItem> _buildFooterItems(AppUser user) {
    return [
      PaneItemSeparator(),
      PaneItem(
        icon: const Icon(FluentIcons.settings),
        title: const Text('Settings'),
        body: const SizedBox.shrink(), // Required body parameter
      ),
      PaneItem(
        icon: const Icon(FluentIcons.contact),
        title: const Text('Profile'),
        body: const SizedBox.shrink(), // Required body parameter
      ),
      PaneItemAction(
        icon: const Icon(FluentIcons.sign_out),
        title: const Text('Logout'),
        onTap: _handleLogout,
      ),
    ];
  }

  Widget _buildHeader(AppUser user, bool isCollapsed) {
    if (isCollapsed) {
      return Container(
        height: 60,
        padding: const EdgeInsets.all(8),
        child: Center(
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                user.nama?.substring(0, 2).toUpperCase() ?? 'UN',
                style: AppTypography.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textOnPrimary,
                ),
              ),
            ),
          ),
        ),
      );
    }

    return Container(
      height: 120,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Text(
                    user.nama?.substring(0, 2).toUpperCase() ?? 'UN',
                    style: AppTypography.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.nama ?? 'Unknown User',
                      style: AppTypography.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      user.roleDisplayName,
                      style: AppTypography.caption.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  FluentIcons.completed_solid,
                  size: 12,
                  color: Colors.green,
                ),
                const SizedBox(width: 4),
                Text(
                  'Online',
                  style: AppTypography.labelSmall.copyWith(color: Colors.green),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleLogout() {
    _logger.i('Logout action triggered');

    final authService = AuthService.instance;
    authService.signOut().then((_) {
      if (mounted) {
        context.go('/login');
      }
    });
  }
}
