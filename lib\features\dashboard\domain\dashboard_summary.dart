import 'package:equatable/equatable.dart';

/// Dashboard summary data model
/// Contains key metrics and statistics for dashboard display
class DashboardSummary extends Equatable {
  /// Total number of portions for the current period
  final int totalPorsi;
  
  /// Number of scheduled deliveries
  final int jadwalPengiriman;
  
  /// Quality control status
  final String statusQc;

  const DashboardSummary({
    required this.totalPorsi,
    required this.jadwalPengiriman,
    required this.statusQc,
  });

  /// Create a copy with updated values
  DashboardSummary copyWith({
    int? totalPorsi,
    int? jadwalPengiriman,
    String? statusQc,
  }) {
    return DashboardSummary(
      totalPorsi: totalPorsi ?? this.totalPorsi,
      jadwalPengiriman: jadwalPengiriman ?? this.jadwalPengiriman,
      statusQc: statusQc ?? this.statusQc,
    );
  }

  /// Create from JSON map
  factory DashboardSummary.fromJson(Map<String, dynamic> json) {
    return DashboardSummary(
      totalPorsi: json['total_porsi'] as int? ?? 0,
      jadwalPengiriman: json['jadwal_pengiriman'] as int? ?? 0,
      statusQc: json['status_qc'] as String? ?? 'Belum Ada Data',
    );
  }

  /// Convert to JSON map
  Map<String, dynamic> toJson() {
    return {
      'total_porsi': totalPorsi,
      'jadwal_pengiriman': jadwalPengiriman,
      'status_qc': statusQc,
    };
  }

  @override
  List<Object?> get props => [totalPorsi, jadwalPengiriman, statusQc];

  @override
  String toString() {
    return 'DashboardSummary(totalPorsi: $totalPorsi, jadwalPengiriman: $jadwalPengiriman, statusQc: $statusQc)';
  }
}
