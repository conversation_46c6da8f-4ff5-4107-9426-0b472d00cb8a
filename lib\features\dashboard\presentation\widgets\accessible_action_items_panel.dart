import 'package:fluent_ui/fluent_ui.dart';
import '../../../../app/constants/app_spacing.dart';
import 'accessibility_utils.dart';
import 'high_contrast_mode.dart';
import 'semantic_label_helper.dart';
import 'keyboard_navigation_helper.dart';

/// Model for action item data
class ActionItem {
  /// Unique identifier
  final String id;

  /// Title of the action
  final String title;

  /// Description of the action
  final String description;

  /// Name of the SPPG
  final String sppgName;

  /// Name of the verifier
  final String verifierName;

  /// Type of action
  final String type;

  /// Creation date
  final DateTime createdAt;

  /// Priority level
  final String priority;

  const ActionItem({
    required this.id,
    required this.title,
    required this.description,
    required this.sppgName,
    required this.verifierName,
    required this.type,
    required this.createdAt,
    required this.priority,
  });
}

/// An accessible action items panel with screen reader support and keyboard navigation
class AccessibleActionItemsPanel extends StatefulWidget {
  /// List of action items to display
  final List<ActionItem> items;

  /// Title of the panel
  final String title;

  /// Callback when an action item is tapped
  final Function(ActionItem) onActionTap;

  /// Maximum number of items to display
  final int maxItems;

  /// Whether to show the "View All" button
  final bool showViewAllButton;

  /// Callback when the "View All" button is tapped
  final VoidCallback? onViewAllTap;

  /// Whether the panel is loading
  final bool isLoading;

  /// Error message to display
  final String? errorMessage;

  const AccessibleActionItemsPanel({
    super.key,
    required this.items,
    required this.title,
    required this.onActionTap,
    this.maxItems = 5,
    this.showViewAllButton = true,
    this.onViewAllTap,
    this.isLoading = false,
    this.errorMessage,
  });

  @override
  State<AccessibleActionItemsPanel> createState() =>
      _AccessibleActionItemsPanelState();
}

class _AccessibleActionItemsPanelState
    extends State<AccessibleActionItemsPanel> {
  final List<FocusNode> _itemFocusNodes = [];
  late FocusNode _viewAllFocusNode;

  @override
  void initState() {
    super.initState();
    _initializeFocusNodes();
  }

  @override
  void didUpdateWidget(AccessibleActionItemsPanel oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus nodes if items changed
    if (widget.items.length != oldWidget.items.length) {
      _disposeFocusNodes();
      _initializeFocusNodes();
    }
  }

  @override
  void dispose() {
    _disposeFocusNodes();
    super.dispose();
  }

  void _initializeFocusNodes() {
    _itemFocusNodes.clear();
    for (int i = 0; i < widget.items.length; i++) {
      _itemFocusNodes.add(FocusNode(debugLabel: 'action_item_$i'));
    }
    _viewAllFocusNode = FocusNode(debugLabel: 'view_all_button');
  }

  void _disposeFocusNodes() {
    for (final node in _itemFocusNodes) {
      node.dispose();
    }
    _itemFocusNodes.clear();
    _viewAllFocusNode.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return HighContrastAwareWidget(
      normalBuilder: (context) => _buildNormalPanel(context),
      highContrastBuilder: (context) => _buildHighContrastPanel(context),
    );
  }

  Widget _buildNormalPanel(BuildContext context) {
    return _buildPanelContent(context);
  }

  Widget _buildHighContrastPanel(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: HighContrastColors.background,
        border: Border.all(color: HighContrastColors.border, width: 2.0),
      ),
      child: _buildPanelContent(context, isHighContrast: true),
    );
  }

  Widget _buildPanelContent(
    BuildContext context, {
    bool isHighContrast = false,
  }) {
    final theme = FluentTheme.of(context);

    // Header with semantic label
    final header = SemanticLabelHelper.addSemanticHeader(
      label: widget.title,
      headerLevel: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Row(
          children: [
            AccessibilityUtils.accessibleIcon(
              FluentIcons.activate_orders,
              context: context,
              color:
                  isHighContrast
                      ? HighContrastColors.accent
                      : theme.accentColor,
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: AccessibilityUtils.accessibleText(
                widget.title,
                context: context,
                style: theme.typography.subtitle?.copyWith(
                  color: isHighContrast ? HighContrastColors.text : null,
                ),
              ),
            ),
          ],
        ),
      ),
    );

    // Content based on state
    Widget content;

    if (widget.isLoading) {
      content = _buildLoadingState(context, isHighContrast);
    } else if (widget.errorMessage != null) {
      content = _buildErrorState(context, isHighContrast);
    } else if (widget.items.isEmpty) {
      content = _buildEmptyState(context, isHighContrast);
    } else {
      content = _buildItemsList(context, isHighContrast);
    }

    // Footer with "View All" button if needed
    Widget? footer;
    if (widget.showViewAllButton &&
        widget.items.isNotEmpty &&
        !widget.isLoading &&
        widget.errorMessage == null) {
      footer = Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: AccessibilityUtils.accessibleButton(
          context: context,
          focusNode: _viewAllFocusNode,
          semanticLabel: 'View all pending actions',
          onPressed: widget.onViewAllTap ?? () {},
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AccessibilityUtils.accessibleText(
                'View All',
                context: context,
                style: TextStyle(
                  color:
                      isHighContrast
                          ? HighContrastColors.accent
                          : theme.accentColor,
                ),
              ),
              const SizedBox(width: AppSpacing.xs),
              Icon(
                FluentIcons.chevron_right,
                size: 12.0,
                color:
                    isHighContrast
                        ? HighContrastColors.accent
                        : theme.accentColor,
              ),
            ],
          ),
        ),
      );
    }

    // Combine all parts
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        header,
        Container(
          height: 1.0,
          color: isHighContrast ? HighContrastColors.border : theme.shadowColor,
        ),
        Expanded(child: content),
        if (footer != null) ...[
          Container(
            height: 1.0,
            color:
                isHighContrast ? HighContrastColors.border : theme.shadowColor,
          ),
          footer,
        ],
      ],
    );
  }

  Widget _buildLoadingState(BuildContext context, bool isHighContrast) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ProgressRing(
            activeColor: isHighContrast ? HighContrastColors.accent : null,
          ),
          const SizedBox(height: AppSpacing.md),
          AccessibilityUtils.accessibleText(
            'Loading pending actions...',
            context: context,
            style: TextStyle(
              color: isHighContrast ? HighContrastColors.text : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, bool isHighContrast) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.error,
            size: 32.0,
            color: isHighContrast ? HighContrastColors.error : Colors.red,
          ),
          const SizedBox(height: AppSpacing.md),
          AccessibilityUtils.accessibleText(
            widget.errorMessage ?? 'Error loading actions',
            context: context,
            style: TextStyle(
              color: isHighContrast ? HighContrastColors.text : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isHighContrast) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.checkbox_composite,
            size: 32.0,
            color:
                isHighContrast
                    ? HighContrastColors.text.withValues(alpha: 0.5)
                    : null,
          ),
          const SizedBox(height: AppSpacing.md),
          AccessibilityUtils.accessibleText(
            'No pending actions',
            context: context,
            style: TextStyle(
              color: isHighContrast ? HighContrastColors.text : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsList(BuildContext context, bool isHighContrast) {
    // Limit items to maxItems
    final displayItems = widget.items.take(widget.maxItems).toList();

    // Create list items
    final itemWidgets = <Widget>[];
    for (int i = 0; i < displayItems.length; i++) {
      final item = displayItems[i];
      itemWidgets.add(
        _buildActionItem(
          context: context,
          item: item,
          focusNode: _itemFocusNodes[i],
          isHighContrast: isHighContrast,
        ),
      );
    }

    // Use keyboard navigation helper for accessible list
    return KeyboardNavigationHelper.createNavigationList(
      context: context,
      children: itemWidgets,
      focusNodes: _itemFocusNodes.take(displayItems.length).toList(),
      spacing: 1.0, // Divider height
    );
  }

  Widget _buildActionItem({
    required BuildContext context,
    required ActionItem item,
    required FocusNode focusNode,
    required bool isHighContrast,
  }) {
    final theme = FluentTheme.of(context);

    // Create semantic label for screen readers
    final semanticLabel = SemanticLabelHelper.createActionItemLabel(
      title: item.title,
      sppgName: item.sppgName,
      priority: item.priority,
      createdAt: _formatDate(item.createdAt),
    );

    // Build the item
    return AccessibilityUtils.withKeyboardFocus(
      focusNode: focusNode,
      child: AccessibilityUtils.withSemanticLabel(
        label: semanticLabel,
        child: GestureDetector(
          onTap: () => widget.onActionTap(item),
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color:
                  focusNode.hasFocus
                      ? isHighContrast
                          ? HighContrastColors.accent.withValues(alpha: 0.2)
                          : theme.accentColor.withValues(alpha: 0.1)
                      : Colors.transparent,
              border: Border(
                bottom: BorderSide(
                  color:
                      isHighContrast
                          ? HighContrastColors.border
                          : theme.shadowColor,
                  width: 1.0,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title and priority
                Row(
                  children: [
                    Expanded(
                      child: AccessibilityUtils.accessibleText(
                        item.title,
                        context: context,
                        style: theme.typography.bodyStrong?.copyWith(
                          color:
                              isHighContrast ? HighContrastColors.text : null,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.sm,
                        vertical: AppSpacing.xs,
                      ),
                      decoration: BoxDecoration(
                        color: _getPriorityColor(item.priority, isHighContrast),
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: AccessibilityUtils.accessibleText(
                        item.priority,
                        context: context,
                        style: TextStyle(
                          fontSize: 12.0,
                          color:
                              isHighContrast
                                  ? HighContrastColors.background
                                  : Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppSpacing.sm),

                // SPPG name and type
                Row(
                  children: [
                    Expanded(
                      child: AccessibilityUtils.accessibleText(
                        item.sppgName,
                        context: context,
                        style: theme.typography.body?.copyWith(
                          color:
                              isHighContrast
                                  ? HighContrastColors.text.withValues(
                                    alpha: 0.8,
                                  )
                                  : null,
                        ),
                      ),
                    ),
                    AccessibilityUtils.accessibleText(
                      item.type,
                      context: context,
                      style: theme.typography.caption?.copyWith(
                        color:
                            isHighContrast
                                ? HighContrastColors.text.withValues(alpha: 0.7)
                                : null,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppSpacing.sm),

                // Verifier and date
                Row(
                  children: [
                    Icon(
                      FluentIcons.user_window,
                      size: 12.0,
                      color:
                          isHighContrast
                              ? HighContrastColors.text.withValues(alpha: 0.7)
                              : theme.inactiveColor,
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    AccessibilityUtils.accessibleText(
                      item.verifierName,
                      context: context,
                      style: theme.typography.caption?.copyWith(
                        color:
                            isHighContrast
                                ? HighContrastColors.text.withValues(alpha: 0.7)
                                : theme.inactiveColor,
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      FluentIcons.calendar,
                      size: 12.0,
                      color:
                          isHighContrast
                              ? HighContrastColors.text.withValues(alpha: 0.7)
                              : theme.inactiveColor,
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    AccessibilityUtils.accessibleText(
                      _formatDate(item.createdAt),
                      context: context,
                      style: theme.typography.caption?.copyWith(
                        color:
                            isHighContrast
                                ? HighContrastColors.text.withValues(alpha: 0.7)
                                : theme.inactiveColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(String priority, bool isHighContrast) {
    if (isHighContrast) {
      switch (priority.toLowerCase()) {
        case 'high':
          return HighContrastColors.error;
        case 'medium':
          return HighContrastColors.warning;
        case 'low':
          return HighContrastColors.info;
        default:
          return HighContrastColors.accent;
      }
    } else {
      switch (priority.toLowerCase()) {
        case 'high':
          return Colors.red;
        case 'medium':
          return Colors.orange;
        case 'low':
          return Colors.blue;
        default:
          return Colors.grey;
      }
    }
  }

  String _formatDate(DateTime date) {
    // Simple date formatting
    return '${date.day}/${date.month}/${date.year}';
  }
}
