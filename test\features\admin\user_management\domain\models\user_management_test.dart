import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/features/admin/user_management/domain/models/user_management.dart';

void main() {
  group('UserManagement Model Tests', () {
    late UserManagement validAdminUser;
    late UserManagement validKepalaDapurUser;
    // late UserManagement validPerwakilanUser; // TODO: Add tests for perwakilan user

    setUp(() {
      validAdminUser = UserManagement(
        id: 'admin-1',
        nama: 'Admin <PERSON>',
        email: '<EMAIL>',
        telepon: '+6281234567890',
        role: UserRole.adminYayasan,
        status: UserStatus.active,
        createdAt: DateTime(2024, 1, 1),
        lastLoginAt: DateTime(2024, 1, 15),
        alamat: 'Jl. Admin No. 123, Jakarta',
        nip: '1234567890123456',
        permissions: {'manage_users': true, 'manage_sppg': true},
        notes: 'Admin utama sistem',
      );

      validKepalaDapurUser = UserManagement(
        id: 'kepala-1',
        nama: 'Kepala Dapur SPPG A',
        email: '<EMAIL>',
        telepon: '081234567890',
        role: UserRole.kepalaDapur,
        status: UserStatus.active,
        sppgId: 'sppg-a-123',
        sppgName: 'SPPG Jakarta Selatan A',
        createdAt: DateTime(2024, 1, 2),
        lastLoginAt: DateTime(2024, 1, 14),
        alamat: 'Jl. Kepala Dapur No. 456, Jakarta Selatan',
        nip: '9876543210987654',
      );

      // validPerwakilanUser = UserManagement( // TODO: Add tests for perwakilan user
      //   id: 'perwakilan-1',
      //   nama: 'Perwakilan Yayasan Jakarta',
      //   email: '<EMAIL>',
      //   telepon: '6281987654321',
      //   role: UserRole.perwakilanYayasan,
      //   status: UserStatus.active,
      //   sppgId: 'sppg-mitra-456',
      //   sppgName: 'SPPG Mitra Jakarta Utara',
      //   createdAt: DateTime(2024, 1, 3),
      //   nip: '1122334455667788',
      // );
    });

    group('Model Properties', () {
      test('should create user with all properties', () {
        expect(validAdminUser.id, 'admin-1');
        expect(validAdminUser.nama, 'Admin Yayasan');
        expect(validAdminUser.email, '<EMAIL>');
        expect(validAdminUser.telepon, '+6281234567890');
        expect(validAdminUser.role, UserRole.adminYayasan);
        expect(validAdminUser.status, UserStatus.active);
        expect(validAdminUser.sppgId, isNull);
        expect(validAdminUser.alamat, 'Jl. Admin No. 123, Jakarta');
        expect(validAdminUser.nip, '1234567890123456');
        expect(validAdminUser.permissions['manage_users'], isTrue);
        expect(validAdminUser.notes, 'Admin utama sistem');
      });

      test('copyWith should update specified properties', () {
        final updated = validAdminUser.copyWith(
          nama: 'Updated Admin',
          status: UserStatus.suspended,
          notes: 'Updated notes',
        );

        expect(updated.nama, 'Updated Admin');
        expect(updated.status, UserStatus.suspended);
        expect(updated.notes, 'Updated notes');
        expect(updated.email, validAdminUser.email); // Unchanged
        expect(updated.id, validAdminUser.id); // Unchanged
      });

      test('should handle equality correctly', () {
        final user1 = validAdminUser;
        final user2 = validAdminUser.copyWith();
        final user3 = validAdminUser.copyWith(nama: 'Different Name');

        expect(user1, equals(user2));
        expect(user1, isNot(equals(user3)));
      });
    });

    group('JSON Serialization', () {
      test('toJson should convert all properties correctly', () {
        final json = validKepalaDapurUser.toJson();

        expect(json['id'], 'kepala-1');
        expect(json['nama'], 'Kepala Dapur SPPG A');
        expect(json['email'], '<EMAIL>');
        expect(json['telepon'], '081234567890');
        expect(json['role'], 'kepalaDapur');
        expect(json['status'], 'active');
        expect(json['sppgId'], 'sppg-a-123');
        expect(json['sppgName'], 'SPPG Jakarta Selatan A');
        expect(json['createdAt'], isA<String>());
        expect(json['lastLoginAt'], isA<String>());
        expect(json['alamat'], 'Jl. Kepala Dapur No. 456, Jakarta Selatan');
        expect(json['nip'], '9876543210987654');
      });

      test('fromJson should recreate object correctly', () {
        final json = validKepalaDapurUser.toJson();
        final recreated = UserManagement.fromJson(json);

        expect(recreated, equals(validKepalaDapurUser));
      });

      test('should handle null values in JSON', () {
        final json = {
          'id': 'test-1',
          'nama': 'Test User',
          'email': '<EMAIL>',
          'telepon': '081234567890',
          'role': 'adminYayasan',
          'status': 'active',
          'createdAt': DateTime.now().toIso8601String(),
          'sppgId': null,
          'sppgName': null,
          'lastLoginAt': null,
          'profileImageUrl': null,
          'alamat': null,
          'nip': null,
          'permissions': null,
          'notes': null,
          'suspendedUntil': null,
        };

        final user = UserManagement.fromJson(json);
        expect(user.sppgId, isNull);
        expect(user.lastLoginAt, isNull);
        expect(user.permissions, isEmpty);
      });
    });

    group('Status and Permission Checks', () {
      test('isActive should return correct status', () {
        expect(validAdminUser.isActive, isTrue);

        final inactiveUser = validAdminUser.copyWith(
          status: UserStatus.inactive,
        );
        expect(inactiveUser.isActive, isFalse);
      });

      test('canLogin should check status and suspension', () {
        expect(validAdminUser.canLogin, isTrue);

        final inactiveUser = validAdminUser.copyWith(
          status: UserStatus.inactive,
        );
        expect(inactiveUser.canLogin, isFalse);

        final suspendedUser = validAdminUser.copyWith(
          status: UserStatus.suspended,
          suspendedUntil: DateTime.now().add(Duration(days: 1)),
        );
        expect(suspendedUser.canLogin, isFalse);

        final expiredSuspension = validAdminUser.copyWith(
          status: UserStatus.suspended,
          suspendedUntil: DateTime.now().subtract(Duration(days: 1)),
        );
        expect(expiredSuspension.canLogin, isTrue);
      });

      test('canBeDeleted should follow business rules', () {
        expect(validAdminUser.canBeDeleted, isFalse); // Admin cannot be deleted
        expect(
          validKepalaDapurUser.canBeDeleted,
          isFalse,
        ); // Active user cannot be deleted

        final inactiveKepala = validKepalaDapurUser.copyWith(
          status: UserStatus.inactive,
        );
        expect(inactiveKepala.canBeDeleted, isTrue);
      });

      test('canBeSuspended should follow business rules', () {
        expect(
          validAdminUser.canBeSuspended,
          isFalse,
        ); // Admin cannot be suspended
        expect(
          validKepalaDapurUser.canBeSuspended,
          isTrue,
        ); // Active non-admin can be suspended

        final inactiveKepala = validKepalaDapurUser.copyWith(
          status: UserStatus.inactive,
        );
        expect(
          inactiveKepala.canBeSuspended,
          isFalse,
        ); // Inactive user cannot be suspended
      });

      test('canBeActivated should check status', () {
        expect(validAdminUser.canBeActivated, isFalse); // Already active

        final inactiveUser = validAdminUser.copyWith(
          status: UserStatus.inactive,
        );
        expect(inactiveUser.canBeActivated, isTrue);

        final suspendedUser = validAdminUser.copyWith(
          status: UserStatus.suspended,
        );
        expect(suspendedUser.canBeActivated, isTrue);

        final pendingUser = validAdminUser.copyWith(status: UserStatus.pending);
        expect(pendingUser.canBeActivated, isTrue);
      });

      test('canResetPassword should check status', () {
        expect(validAdminUser.canResetPassword, isTrue); // Active user

        final pendingUser = validAdminUser.copyWith(status: UserStatus.pending);
        expect(pendingUser.canResetPassword, isTrue);

        final inactiveUser = validAdminUser.copyWith(
          status: UserStatus.inactive,
        );
        expect(inactiveUser.canResetPassword, isFalse);

        final suspendedUser = validAdminUser.copyWith(
          status: UserStatus.suspended,
        );
        expect(suspendedUser.canResetPassword, isFalse);
      });

      test('hasPermission should check permissions map', () {
        expect(validAdminUser.hasPermission('manage_users'), isTrue);
        expect(validAdminUser.hasPermission('manage_sppg'), isTrue);
        expect(validAdminUser.hasPermission('nonexistent'), isFalse);

        expect(validKepalaDapurUser.hasPermission('manage_users'), isFalse);
      });
    });

    group('Display Methods', () {
      test('lastLoginDisplay should format time correctly', () {
        final now = DateTime.now();

        // Never logged in
        final neverLoggedIn = UserManagement(
          id: 'never-logged-in',
          nama: 'Never Logged In User',
          email: '<EMAIL>',
          telepon: '+6281234567890',
          role: UserRole.adminYayasan,
          status: UserStatus.active,
          createdAt: DateTime.now(),
          // lastLoginAt is null by default
        );
        expect(neverLoggedIn.lastLoginDisplay, 'Belum pernah login');

        // Just now
        final justNow = validAdminUser.copyWith(
          lastLoginAt: now.subtract(Duration(seconds: 30)),
        );
        expect(justNow.lastLoginDisplay, 'Baru saja');

        // Minutes ago
        final minutesAgo = validAdminUser.copyWith(
          lastLoginAt: now.subtract(Duration(minutes: 30)),
        );
        expect(minutesAgo.lastLoginDisplay, '30 menit yang lalu');

        // Hours ago
        final hoursAgo = validAdminUser.copyWith(
          lastLoginAt: now.subtract(Duration(hours: 5)),
        );
        expect(hoursAgo.lastLoginDisplay, '5 jam yang lalu');

        // Days ago (within a week)
        final daysAgo = validAdminUser.copyWith(
          lastLoginAt: now.subtract(Duration(days: 3)),
        );
        expect(daysAgo.lastLoginDisplay, '3 hari yang lalu');

        // More than a week ago (should show date)
        final longAgo = validAdminUser.copyWith(
          lastLoginAt: DateTime(2024, 1, 1),
        );
        expect(longAgo.lastLoginDisplay, contains('/'));
      });

      test('statusDisplay should include emoji and text', () {
        expect(validAdminUser.statusDisplay, '✅ Aktif');

        final inactiveUser = validAdminUser.copyWith(
          status: UserStatus.inactive,
        );
        expect(inactiveUser.statusDisplay, '⚪ Tidak Aktif');

        final suspendedUser = validAdminUser.copyWith(
          status: UserStatus.suspended,
        );
        expect(suspendedUser.statusDisplay, '🔴 Ditangguhkan');

        final pendingUser = validAdminUser.copyWith(status: UserStatus.pending);
        expect(pendingUser.statusDisplay, '🟡 Menunggu Verifikasi');
      });
    });

    group('Validation Tests', () {
      group('validateForCreation', () {
        test('should pass for valid admin user', () {
          final result = validAdminUser.validateForCreation();
          expect(result.isValid, isTrue);
        });

        test('should pass for valid kepala dapur user', () {
          final result = validKepalaDapurUser.validateForCreation();
          expect(result.isValid, isTrue);
        });

        test('should fail when nama is empty', () {
          final user = validAdminUser.copyWith(nama: '');
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('Nama'), isTrue);
        });

        test('should fail when nama is too short', () {
          final user = validAdminUser.copyWith(nama: 'A');
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('Nama'), isTrue);
        });

        test('should fail when nama is too long', () {
          final longName = 'A' * 101;
          final user = validAdminUser.copyWith(nama: longName);
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('Nama'), isTrue);
        });

        test('should fail for invalid email format', () {
          final user = validAdminUser.copyWith(email: 'invalid-email');
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('Email'), isTrue);
        });

        test('should fail for invalid phone number', () {
          final user = validAdminUser.copyWith(telepon: '123');
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('Nomor Telepon'), isTrue);
        });
      });

      group('Role-based Validation', () {
        test('should fail when non-admin role has no SPPG assignment', () {
          final user = validKepalaDapurUser.copyWith(sppgId: null);
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('sppgId'), isTrue);
          expect(
            result.getFieldError('sppgId'),
            contains('SPPG wajib ditugaskan'),
          );
        });

        test('should fail when admin role has SPPG assignment', () {
          final user = validAdminUser.copyWith(sppgId: 'some-sppg-id');
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('sppgId'), isTrue);
          expect(
            result.getFieldError('sppgId'),
            contains('tidak memerlukan penugasan SPPG'),
          );
        });

        test('should fail when role requiring NIP has no NIP', () {
          final user = validKepalaDapurUser.copyWith(nip: null);
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('nip'), isTrue);
          expect(result.getFieldError('nip'), contains('NIP wajib diisi'));
        });

        test('should pass when admin role has no NIP', () {
          final user = validAdminUser.copyWith(nip: null);
          final result = user.validateForCreation();

          // Should still be valid since admin doesn't require NIP
          expect(result.hasFieldError('nip'), isFalse);
        });
      });

      group('Optional Fields Validation', () {
        test('should fail for invalid NIP format', () {
          final user = validKepalaDapurUser.copyWith(nip: '123abc');
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('nip'), isTrue);
          expect(
            result.getFieldError('nip'),
            contains('Format NIP tidak valid'),
          );
        });

        test('should fail for NIP that is too short', () {
          final user = validKepalaDapurUser.copyWith(nip: '1234567');
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('nip'), isTrue);
        });

        test('should fail for NIP that is too long', () {
          final user = validKepalaDapurUser.copyWith(
            nip: '123456789012345678901',
          );
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('nip'), isTrue);
        });

        test('should fail for alamat that is too long', () {
          final longAddress = 'A' * 501;
          final user = validAdminUser.copyWith(alamat: longAddress);
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('alamat'), isTrue);
          expect(
            result.getFieldError('alamat'),
            contains('maksimal 500 karakter'),
          );
        });

        test('should fail for notes that are too long', () {
          final longNotes = 'A' * 1001;
          final user = validAdminUser.copyWith(notes: longNotes);
          final result = user.validateForCreation();

          expect(result.isValid, isFalse);
          expect(result.hasFieldError('notes'), isTrue);
          expect(
            result.getFieldError('notes'),
            contains('maksimal 1000 karakter'),
          );
        });

        test('should pass with valid optional fields', () {
          final user = validAdminUser.copyWith(
            alamat: 'Valid address under 500 characters',
            notes: 'Valid notes under 1000 characters',
            nip: null, // Admin doesn't require NIP
          );
          final result = user.validateForCreation();

          expect(result.isValid, isTrue);
        });
      });
    });

    group('Password Generation', () {
      test(
        'generateTemporaryPassword should create password of specified length',
        () {
          final password = UserManagement.generateTemporaryPassword(length: 8);
          expect(password.length, 8);

          final defaultPassword = UserManagement.generateTemporaryPassword();
          expect(defaultPassword.length, 12);
        },
      );

      test(
        'generateSecurePassword should create password with required complexity',
        () {
          final password = UserManagement.generateSecurePassword();

          expect(password.length, 12);
          expect(password, matches(RegExp(r'[a-z]'))); // Has lowercase
          expect(password, matches(RegExp(r'[A-Z]'))); // Has uppercase
          expect(password, matches(RegExp(r'[0-9]'))); // Has numbers
          expect(password, matches(RegExp(r'[!@#\$%^&*]'))); // Has symbols
        },
      );

      test('generated passwords should be different each time', () {
        final password1 = UserManagement.generateSecurePassword();
        final password2 = UserManagement.generateSecurePassword();

        expect(password1, isNot(equals(password2)));
      });
    });

    group('Data Transformation', () {
      test('toCreateRequest should format data correctly', () {
        final request = validKepalaDapurUser.toCreateRequest();

        expect(request['nama'], 'Kepala Dapur SPPG A');
        expect(request['email'], '<EMAIL>');
        expect(request['telepon'], '+6281234567890'); // Normalized
        expect(request['role'], 'kepalaDapur');
        expect(request['status'], 'active');
        expect(request['sppg_id'], 'sppg-a-123');
        expect(request['alamat'], 'Jl. Kepala Dapur No. 456, Jakarta Selatan');
        expect(request['nip'], '9876543210987654');
      });

      test('toUpdateRequest should include ID and timestamp', () {
        final request = validKepalaDapurUser.toUpdateRequest();

        expect(request['id'], 'kepala-1');
        expect(request['updated_at'], isA<String>());
        expect(request['nama'], 'Kepala Dapur SPPG A');
      });

      test('normalize should clean and format data', () {
        final user = validAdminUser.copyWith(
          nama: '  Admin Yayasan  ',
          email: '<EMAIL>',
          telepon: '081234567890',
          alamat: '  Jl. Admin No. 123  ',
          nip: '  1234567890123456  ',
          notes: '  Admin utama sistem  ',
        );

        final normalized = user.normalize();

        expect(normalized.nama, 'Admin Yayasan');
        expect(normalized.email, '<EMAIL>');
        expect(normalized.telepon, '+6281234567890');
        expect(normalized.alamat, 'Jl. Admin No. 123');
        expect(normalized.nip, '1234567890123456');
        expect(normalized.notes, 'Admin utama sistem');
      });
    });
  });

  group('UserRole Enum Tests', () {
    test('should have correct display names', () {
      expect(UserRole.adminYayasan.displayName, 'Admin Yayasan');
      expect(UserRole.perwakilanYayasan.displayName, 'Perwakilan Yayasan');
      expect(UserRole.kepalaDapur.displayName, 'Kepala Dapur');
      expect(UserRole.ahliGizi.displayName, 'Ahli Gizi');
      expect(UserRole.akuntan.displayName, 'Akuntan');
      expect(
        UserRole.pengawasPemeliharaan.displayName,
        'Pengawas Pemeliharaan',
      );
    });

    test('requiresSppgAssignment should be correct for each role', () {
      expect(UserRole.adminYayasan.requiresSppgAssignment, isFalse);
      expect(UserRole.perwakilanYayasan.requiresSppgAssignment, isTrue);
      expect(UserRole.kepalaDapur.requiresSppgAssignment, isTrue);
      expect(UserRole.ahliGizi.requiresSppgAssignment, isTrue);
      expect(UserRole.akuntan.requiresSppgAssignment, isTrue);
      expect(UserRole.pengawasPemeliharaan.requiresSppgAssignment, isTrue);
    });

    test('managableRoles should include all roles', () {
      expect(UserRole.managableRoles, equals(UserRole.values));
    });
  });

  group('UserStatus Enum Tests', () {
    test('should have correct display names', () {
      expect(UserStatus.active.displayName, 'Aktif');
      expect(UserStatus.inactive.displayName, 'Tidak Aktif');
      expect(UserStatus.suspended.displayName, 'Ditangguhkan');
      expect(UserStatus.pending.displayName, 'Menunggu Verifikasi');
    });
  });

  group('UserFilter Tests', () {
    late List<UserManagement> testUsers;

    setUp(() {
      testUsers = [
        UserManagement(
          id: '1',
          nama: 'Admin Test',
          email: '<EMAIL>',
          telepon: '+6281111111111',
          role: UserRole.adminYayasan,
          status: UserStatus.active,
          createdAt: DateTime.now(),
        ),
        UserManagement(
          id: '2',
          nama: 'Kepala Dapur A',
          email: '<EMAIL>',
          telepon: '+6282222222222',
          role: UserRole.kepalaDapur,
          status: UserStatus.active,
          sppgId: 'sppg-a',
          sppgName: 'SPPG Jakarta A',
          createdAt: DateTime.now(),
        ),
        UserManagement(
          id: '3',
          nama: 'Perwakilan Jakarta',
          email: '<EMAIL>',
          telepon: '+6283333333333',
          role: UserRole.perwakilanYayasan,
          status: UserStatus.suspended,
          sppgId: 'sppg-b',
          sppgName: 'SPPG Jakarta B',
          createdAt: DateTime.now(),
        ),
      ];
    });

    test('empty filter should match all users', () {
      const filter = UserFilter();
      expect(filter.isEmpty, isTrue);

      for (final user in testUsers) {
        expect(filter.matches(user), isTrue);
      }
    });

    test('search query should match name, email, or SPPG name', () {
      const filter = UserFilter(searchQuery: 'jakarta');

      expect(
        filter.matches(testUsers[0]),
        isFalse,
      ); // Admin doesn't have Jakarta
      expect(
        filter.matches(testUsers[1]),
        isTrue,
      ); // SPPG name contains Jakarta
      expect(filter.matches(testUsers[2]), isTrue); // Name contains Jakarta
    });

    test('role filter should match specific role', () {
      const filter = UserFilter(role: UserRole.kepalaDapur);

      expect(filter.matches(testUsers[0]), isFalse);
      expect(filter.matches(testUsers[1]), isTrue);
      expect(filter.matches(testUsers[2]), isFalse);
    });

    test('status filter should match specific status', () {
      const filter = UserFilter(status: UserStatus.suspended);

      expect(filter.matches(testUsers[0]), isFalse);
      expect(filter.matches(testUsers[1]), isFalse);
      expect(filter.matches(testUsers[2]), isTrue);
    });

    test('SPPG filter should match specific SPPG', () {
      const filter = UserFilter(sppgId: 'sppg-a');

      expect(filter.matches(testUsers[0]), isFalse);
      expect(filter.matches(testUsers[1]), isTrue);
      expect(filter.matches(testUsers[2]), isFalse);
    });

    test('requiresSppgAssignment filter should work correctly', () {
      const filter = UserFilter(requiresSppgAssignment: true);

      expect(
        filter.matches(testUsers[0]),
        isFalse,
      ); // Admin doesn't require SPPG
      expect(
        filter.matches(testUsers[1]),
        isTrue,
      ); // Kepala Dapur requires SPPG
      expect(filter.matches(testUsers[2]), isTrue); // Perwakilan requires SPPG
    });

    test('multiple filters should work together', () {
      const filter = UserFilter(
        role: UserRole.kepalaDapur,
        status: UserStatus.active,
        searchQuery: 'kepala',
      );

      expect(filter.matches(testUsers[0]), isFalse);
      expect(filter.matches(testUsers[1]), isTrue);
      expect(filter.matches(testUsers[2]), isFalse);
    });

    test('copyWith should update specified filters', () {
      const original = UserFilter(role: UserRole.adminYayasan);
      final updated = original.copyWith(status: UserStatus.active);

      expect(updated.role, UserRole.adminYayasan);
      expect(updated.status, UserStatus.active);
      expect(updated.searchQuery, isNull);
    });
  });
}
