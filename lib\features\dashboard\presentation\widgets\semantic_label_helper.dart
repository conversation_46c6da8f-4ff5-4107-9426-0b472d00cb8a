import 'package:fluent_ui/fluent_ui.dart';

/// Helper class for semantic labels in dashboard components
class SemanticLabelHelper {
  /// Private constructor
  SemanticLabelHelper._();

  /// Create a semantic label for a KPI card
  static String createKpiLabel({
    required String title,
    required String value,
    String? trend,
    String? subtitle,
  }) {
    final buffer = StringBuffer();
    buffer.write('$title: $value');

    if (trend != null && trend.isNotEmpty) {
      buffer.write(', $trend');
    }

    if (subtitle != null && subtitle.isNotEmpty) {
      buffer.write(', $subtitle');
    }

    return buffer.toString();
  }

  /// Create a semantic label for an action item
  static String createActionItemLabel({
    required String title,
    required String sppgName,
    String? priority,
    String? createdAt,
  }) {
    final buffer = StringBuffer();
    buffer.write('Action required: $title');
    buffer.write(' for $sppgName');

    if (priority != null && priority.isNotEmpty) {
      buffer.write(', Priority: $priority');
    }

    if (createdAt != null && createdAt.isNotEmpty) {
      buffer.write(', Created: $createdAt');
    }

    return buffer.toString();
  }

  /// Create a semantic label for a map location
  static String createMapLocationLabel({
    required String name,
    required String status,
    String? type,
    String? address,
  }) {
    final buffer = StringBuffer();
    buffer.write('SPPG Location: $name');
    buffer.write(', Status: $status');

    if (type != null && type.isNotEmpty) {
      buffer.write(', Type: $type');
    }

    if (address != null && address.isNotEmpty) {
      buffer.write(', Address: $address');
    }

    return buffer.toString();
  }

  /// Create a semantic label for a chart
  static String createChartLabel({
    required String title,
    required String description,
    int? dataPointCount,
  }) {
    final buffer = StringBuffer();
    buffer.write('Chart: $title');
    buffer.write(', $description');

    if (dataPointCount != null) {
      buffer.write(', Contains $dataPointCount data points');
    }

    return buffer.toString();
  }

  /// Create a semantic label for an activity feed item
  static String createActivityLabel({
    required String title,
    required String timestamp,
    String? sppgName,
    String? severity,
  }) {
    final buffer = StringBuffer();
    buffer.write('Activity: $title');
    buffer.write(', Time: $timestamp');

    if (sppgName != null && sppgName.isNotEmpty) {
      buffer.write(', SPPG: $sppgName');
    }

    if (severity != null && severity.isNotEmpty) {
      buffer.write(', Severity: $severity');
    }

    return buffer.toString();
  }

  /// Create a semantic label for a navigation item
  static String createNavigationItemLabel({
    required String title,
    int? notificationCount,
  }) {
    final buffer = StringBuffer();
    buffer.write('Navigation: $title');

    if (notificationCount != null && notificationCount > 0) {
      buffer.write(', $notificationCount notifications');
    }

    return buffer.toString();
  }

  /// Add semantic label to a widget
  static Widget addSemanticLabel({
    required Widget child,
    required String label,
    bool excludeSemantics = false,
  }) {
    return Semantics(
      label: label,
      excludeSemantics: excludeSemantics,
      child: child,
    );
  }

  /// Add semantic button properties to a widget
  static Widget addSemanticButton({
    required Widget child,
    required String label,
    required bool enabled,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: label,
      button: true,
      enabled: enabled,
      onTap: onTap,
      child: child,
    );
  }

  /// Add semantic header properties to a widget
  static Widget addSemanticHeader({
    required Widget child,
    required String label,
    int headerLevel = 1,
  }) {
    return Semantics(label: label, header: true, child: child);
  }

  /// Add semantic image properties to a widget
  static Widget addSemanticImage({
    required Widget child,
    required String label,
  }) {
    return Semantics(label: label, image: true, child: child);
  }

  /// Add live region semantic for dynamic content
  static Widget addLiveRegion({
    required Widget child,
    String? label,
    bool assertive = false,
  }) {
    return Semantics(label: label, liveRegion: true, child: child);
  }
}
