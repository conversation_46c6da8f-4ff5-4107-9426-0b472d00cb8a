import '../../../../core/auth/domain/simplified_app_user.dart';
import '../entities/dashboard_configuration.dart';

/// Service for handling dashboard permission checks and access control
class DashboardPermissionService {
  /// Check if user has permission to view a specific dashboard component
  static bool hasComponentPermission(AppUser user, ComponentConfig component) {
    // If no permissions required, allow access
    if (component.requiredPermissions.isEmpty) {
      return true;
    }

    // <PERSON><PERSON> has access to all components
    if (user.isAdmin<PERSON><PERSON><PERSON>) {
      return true;
    }

    // Check if user has any of the required permissions
    return component.requiredPermissions.any(
      (permission) => _userHasPermission(user, permission),
    );
  }

  /// Check if user has permission to view a navigation section
  static bool hasSectionPermission(AppUser user, NavigationSection section) {
    // If no permissions required, allow access
    if (section.requiredPermissions.isEmpty) {
      return true;
    }

    // Admin <PERSON> has access to all sections
    if (user.isAdmin<PERSON><PERSON><PERSON>) {
      return true;
    }

    // Check if user has any of the required permissions
    return section.requiredPermissions.any(
      (permission) => _userHasPermission(user, permission),
    );
  }

  /// Check if user has permission to view a navigation item
  static bool hasNavigationItemPermission(AppUser user, NavigationItem item) {
    // If no permissions required, allow access
    if (item.requiredPermissions.isEmpty) {
      return true;
    }

    // Admin Yayasan has access to all navigation items
    if (user.isAdminYayasan) {
      return true;
    }

    // Check if user has any of the required permissions
    return item.requiredPermissions.any(
      (permission) => _userHasPermission(user, permission),
    );
  }

  /// Check if user can access the dashboard at all
  static bool canAccessDashboard(AppUser user) {
    // Guest users cannot access dashboard
    if (user.isGuest) {
      return false;
    }

    // All authenticated users can access dashboard
    // Note: Account status checks would be handled at the auth service level
    return true;
  }

  /// Get filtered components based on user permissions
  static List<ComponentConfig> getAccessibleComponents(
    AppUser user,
    List<ComponentConfig> components,
  ) {
    return components
        .where((component) => hasComponentPermission(user, component))
        .toList();
  }

  /// Get filtered navigation sections based on user permissions
  static List<NavigationSection> getAccessibleSections(
    AppUser user,
    List<NavigationSection> sections,
  ) {
    return sections
        .where((section) => hasSectionPermission(user, section))
        .map((section) => _filterSectionItems(user, section))
        .where((section) => section.items.isNotEmpty)
        .toList();
  }

  /// Filter navigation items within a section based on permissions
  static NavigationSection _filterSectionItems(
    AppUser user,
    NavigationSection section,
  ) {
    final accessibleItems =
        section.items
            .where((item) => hasNavigationItemPermission(user, item))
            .toList();

    return NavigationSection(
      title: section.title,
      items: accessibleItems,
      requiredPermissions: section.requiredPermissions,
      isCollapsible: section.isCollapsible,
      defaultExpanded: section.defaultExpanded,
    );
  }

  /// Check if user has a specific permission
  static bool _userHasPermission(AppUser user, String permission) {
    // Admin Yayasan has all permissions
    if (user.isAdminYayasan) {
      return true;
    }

    // Check role-based permissions
    switch (permission) {
      case 'dashboard':
        return !user.isGuest; // All authenticated users can access dashboard

      case 'kpi_view':
        return user.isAdminYayasan ||
            user.isPerwakilanYayasan ||
            user.isKepalaDapur ||
            user.isAhliGizi ||
            user.isAkuntan;

      case 'pending_actions':
        return user.isAdminYayasan || user.isPerwakilanYayasan;

      case 'sppg_map':
        return user.isAdminYayasan || user.isPerwakilanYayasan;

      case 'performance_chart':
        return user.isAdminYayasan ||
            user.isPerwakilanYayasan ||
            user.isKepalaDapur;

      case 'activity_feed':
        return !user.isGuest; // All authenticated users can see activities

      case 'sppg_management':
        return user.isAdminYayasan || user.isPerwakilanYayasan;

      case 'user_management':
        return user.isAdminYayasan || user.isPerwakilanYayasan;

      case 'kitchen_management':
        return user.isAdminYayasan ||
            user.isPerwakilanYayasan ||
            user.isKepalaDapur ||
            user.isAhliGizi;

      case 'inventory_management':
        return user.isAdminYayasan ||
            user.isPerwakilanYayasan ||
            user.isKepalaDapur ||
            user.isAhliGizi ||
            user.isAkuntan;

      case 'financial_management':
        return user.isAdminYayasan ||
            user.isPerwakilanYayasan ||
            user.isAkuntan;

      case 'delivery_management':
        return user.isAdminYayasan ||
            user.isPerwakilanYayasan ||
            user.isKepalaDapur ||
            user.isPengawasPemeliharaan;

      case 'reporting':
        return !user.isGuest; // All authenticated users can access reports

      case 'settings':
        return !user.isGuest; // All authenticated users can access settings

      default:
        // Use the existing hasAccessTo method for other permissions
        return user.hasAccessTo(permission);
    }
  }

  /// Get access denied reason for a component
  static String getAccessDeniedReason(AppUser user, ComponentConfig component) {
    if (user.isGuest) {
      return 'Anda harus login untuk mengakses fitur ini';
    }

    if (component.requiredPermissions.isEmpty) {
      return 'Akses ditolak karena alasan yang tidak diketahui';
    }

    final missingPermissions =
        component.requiredPermissions
            .where((permission) => !_userHasPermission(user, permission))
            .toList();

    if (missingPermissions.isNotEmpty) {
      return 'Anda tidak memiliki izin untuk mengakses fitur ini. '
          'Diperlukan: ${missingPermissions.join(', ')}';
    }

    return 'Akses ditolak';
  }

  /// Check if user's role allows configuration loading
  static bool canLoadConfiguration(AppUser user, String roleId) {
    // User must be authenticated
    if (user.isGuest) {
      return false;
    }

    // User's role must match the configuration role or be admin
    return user.role == roleId || user.isAdminYayasan;
  }

  /// Get appropriate dashboard configuration role for user
  static String getDashboardRoleForUser(AppUser user) {
    if (user.isGuest) {
      throw Exception('Guest users cannot access dashboard');
    }

    // Return user's actual role for configuration loading
    return user.role;
  }

  /// Validate user session for dashboard access
  static DashboardAccessValidation validateDashboardAccess(AppUser user) {
    // Check if user is authenticated
    if (user.isGuest) {
      return DashboardAccessValidation.denied(
        reason: 'Anda harus login untuk mengakses dashboard',
        requiresLogin: true,
      );
    }

    // Note: Password change requirements would be handled at the auth service level

    // Check if email is verified (if required)
    if (!user.emailVerified && user.email != null) {
      return DashboardAccessValidation.warning(
        reason:
            'Email Anda belum diverifikasi. Beberapa fitur mungkin terbatas',
        allowAccess: true,
      );
    }

    // All checks passed
    return DashboardAccessValidation.allowed();
  }
}

/// Result of dashboard access validation
class DashboardAccessValidation {
  final bool isAllowed;
  final bool isWarning;
  final String? reason;
  final bool requiresLogin;
  final bool requiresPasswordChange;

  const DashboardAccessValidation._({
    required this.isAllowed,
    this.isWarning = false,
    this.reason,
    this.requiresLogin = false,
    this.requiresPasswordChange = false,
  });

  factory DashboardAccessValidation.allowed() {
    return const DashboardAccessValidation._(isAllowed: true);
  }

  factory DashboardAccessValidation.denied({
    required String reason,
    bool requiresLogin = false,
    bool requiresPasswordChange = false,
  }) {
    return DashboardAccessValidation._(
      isAllowed: false,
      reason: reason,
      requiresLogin: requiresLogin,
      requiresPasswordChange: requiresPasswordChange,
    );
  }

  factory DashboardAccessValidation.warning({
    required String reason,
    bool allowAccess = true,
  }) {
    return DashboardAccessValidation._(
      isAllowed: allowAccess,
      isWarning: true,
      reason: reason,
    );
  }
}
