import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../config/supabase_service.dart';

/// Utility untuk debugging masalah email confirmation
/// 
/// Tools untuk:
/// 1. Cek status email confirmation user
/// 2. Manual confirm email (development only)
/// 3. Test login dengan email yang belum dikonfirmasi
class EmailConfirmationDebugger {
  static final Logger _logger = Logger();
  
  /// Cek status email confirmation untuk user tertentu
  static Future<Map<String, dynamic>> checkEmailStatus(String email) async {
    try {
      _logger.d('Checking email status for: $email');
      
      final supabase = SupabaseService.instance;
      
      // Query user from auth.users table
      final response = await supabase.client
          .from('auth.users')
          .select('id, email, email_confirmed_at, created_at')
          .eq('email', email)
          .maybeSingle();
      
      if (response == null) {
        return {
          'found': false,
          'message': 'User with email $email not found'
        };
      }
      
      final emailConfirmedAt = response['email_confirmed_at'];
      final isConfirmed = emailConfirmedAt != null;
      
      return {
        'found': true,
        'confirmed': isConfirmed,
        'email': response['email'],
        'confirmed_at': emailConfirmedAt,
        'created_at': response['created_at'],
        'user_id': response['id'],
        'message': isConfirmed 
            ? 'Email sudah dikonfirmasi pada $emailConfirmedAt'
            : 'Email belum dikonfirmasi'
      };
      
    } catch (e, stackTrace) {
      _logger.e('Error checking email status: $e', stackTrace: stackTrace);
      return {
        'found': false,
        'error': true,
        'message': 'Error: $e'
      };
    }
  }
  
  /// Manual confirm email (DEVELOPMENT ONLY!)
  /// JANGAN gunakan di production!
  static Future<bool> manualConfirmEmail(String email) async {
    try {
      _logger.w('MANUAL EMAIL CONFIRMATION - DEVELOPMENT ONLY!');
      _logger.d('Manually confirming email for: $email');
      
      final supabase = SupabaseService.instance;
      
      // Update email_confirmed_at to current timestamp
      final response = await supabase.client.rpc(
        'confirm_email_manual',
        params: {'user_email': email}
      );
      
      _logger.i('Manual email confirmation result: $response');
      return true;
      
    } catch (e, stackTrace) {
      _logger.e('Error manually confirming email: $e', stackTrace: stackTrace);
      
      // Fallback: Try direct SQL update (jika ada permission)
      try {
        _logger.d('Trying fallback method...');
        
        final supabase = SupabaseService.instance;
        final now = DateTime.now().toIso8601String();
        
        // Ini mungkin tidak akan bekerja karena security restrictions
        await supabase.client.rpc(
          'update_email_confirmed',
          params: {
            'user_email': email,
            'confirmed_at': now
          }
        );
        
        return true;
        
      } catch (fallbackError) {
        _logger.e('Fallback method also failed: $fallbackError');
        return false;
      }
    }
  }
  
  /// Test login tanpa email confirmation
  static Future<Map<String, dynamic>> testLogin(String email, String password) async {
    try {
      _logger.d('Testing login for potentially unconfirmed email: $email');
      
      final supabase = SupabaseService.instance;
      
      // Attempt sign in
      final response = await supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.user != null) {
        _logger.i('Login successful!');
        return {
          'success': true,
          'user_id': response.user!.id,
          'email': response.user!.email,
          'email_confirmed': response.user!.emailConfirmedAt != null,
          'message': 'Login berhasil'
        };
      } else {
        _logger.w('Login failed - no user returned');
        return {
          'success': false,
          'message': 'Login gagal - tidak ada user yang dikembalikan'
        };
      }
      
    } catch (e, stackTrace) {
      _logger.e('Login test failed: $e', stackTrace: stackTrace);
      
      // Check if error is related to email confirmation
      final errorMessage = e.toString().toLowerCase();
      final isEmailConfirmationError = errorMessage.contains('email') && 
                                     errorMessage.contains('confirm');
      
      return {
        'success': false,
        'email_confirmation_error': isEmailConfirmationError,
        'error_message': e.toString(),
        'message': isEmailConfirmationError 
            ? 'Error: Email belum dikonfirmasi'
            : 'Error login: $e'
      };
    }
  }
  
  /// Show debug dialog dengan informasi email confirmation
  static void showEmailDebugDialog(BuildContext context, String email) {
    showDialog(
      context: context,
      builder: (context) => EmailDebugDialog(email: email),
    );
  }
}

/// Dialog untuk debugging email confirmation issues
class EmailDebugDialog extends StatefulWidget {
  final String email;
  
  const EmailDebugDialog({
    super.key,
    required this.email,
  });
  
  @override
  State<EmailDebugDialog> createState() => _EmailDebugDialogState();
}

class _EmailDebugDialogState extends State<EmailDebugDialog> {
  Map<String, dynamic>? _emailStatus;
  bool _isLoading = false;
  
  @override
  void initState() {
    super.initState();
    _checkEmailStatus();
  }
  
  Future<void> _checkEmailStatus() async {
    setState(() => _isLoading = true);
    
    final status = await EmailConfirmationDebugger.checkEmailStatus(widget.email);
    
    setState(() {
      _emailStatus = status;
      _isLoading = false;
    });
  }
  
  Future<void> _manualConfirm() async {
    setState(() => _isLoading = true);
    
    final success = await EmailConfirmationDebugger.manualConfirmEmail(widget.email);
    
    if (success) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Email berhasil dikonfirmasi secara manual'),
            backgroundColor: Colors.green,
          ),
        );
      }
      await _checkEmailStatus(); // Refresh status
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Gagal konfirmasi email secara manual'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
    
    setState(() => _isLoading = false);
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Email Confirmation Debug'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Email: ${widget.email}'),
            const SizedBox(height: 16),
            
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_emailStatus != null) ...[
              
              Text('Status: ${_emailStatus!['found'] ? 'Ditemukan' : 'Tidak ditemukan'}'),
              
              if (_emailStatus!['found']) ...[
                Text('Konfirmasi: ${_emailStatus!['confirmed'] ? 'Sudah' : 'Belum'}'),
                if (_emailStatus!['confirmed_at'] != null)
                  Text('Dikonfirmasi: ${_emailStatus!['confirmed_at']}'),
                Text('User ID: ${_emailStatus!['user_id']}'),
              ],
              
              const SizedBox(height: 8),
              Text(
                _emailStatus!['message'],
                style: TextStyle(
                  color: _emailStatus!['confirmed'] == true 
                      ? Colors.green 
                      : Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              if (!_emailStatus!['confirmed'] && _emailStatus!['found']) ...[
                const SizedBox(height: 16),
                const Text(
                  'DEVELOPMENT ONLY:',
                  style: TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton(
                  onPressed: _manualConfirm,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                  ),
                  child: const Text('Manual Confirm Email'),
                ),
              ],
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
        TextButton(
          onPressed: _checkEmailStatus,
          child: const Text('Refresh'),
        ),
      ],
    );
  }
}
