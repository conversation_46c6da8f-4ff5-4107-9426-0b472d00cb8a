import '../../domain/entities/entities.dart';
import '../../domain/repositories/dashboard_configuration_repository.dart';
import '../datasources/dashboard_configuration_local_datasource.dart';
import '../models/dashboard_configuration_model.dart';
import '../validators/dashboard_configuration_validator.dart';
import 'package:logger/logger.dart';

/// Implementation of dashboard configuration repository
///
/// Handles loading, caching, and validation of dashboard configurations
class DashboardConfigurationRepositoryImpl
    implements DashboardConfigurationRepository {
  final DashboardConfigurationLocalDataSource _localDataSource;
  final Logger _logger = Logger();

  DashboardConfigurationRepositoryImpl(this._localDataSource);

  @override
  Future<DashboardConfiguration?> getConfigurationForRole(String roleId) async {
    try {
      // First try to load custom configuration (user modifications)
      var configModel = await _localDataSource.loadCustomConfiguration(roleId);

      // If no custom config, try cached configuration
      configModel ??= await _localDataSource.getCachedConfiguration(roleId);

      // If no cached config, load from assets and cache it
      if (configModel == null) {
        configModel = await _localDataSource.loadConfigurationFromAssets(
          roleId,
        );
        if (configModel != null) {
          // Cache the loaded configuration
          await _localDataSource.cacheConfiguration(configModel);
        }
      }

      // Validate configuration before returning
      if (configModel != null) {
        final validationResult = DashboardConfigurationValidator.validate(
          configModel,
        );
        if (!validationResult.isValid) {
          // Log validation errors but still return the config
          // In production, you might want to handle this differently
          _logger.w(
            'Configuration validation failed for role $roleId: ${validationResult.summary}',
          );
        }

        return configModel.toDomain();
      }

      return null;
    } catch (e) {
      // Log error and return null
      _logger.e('Error loading configuration for role $roleId: $e');
      return null;
    }
  }

  @override
  Future<List<DashboardConfiguration>> getAllConfigurations() async {
    final configurations = <DashboardConfiguration>[];

    // Get all cached role IDs
    final cachedRoleIds = await _localDataSource.getCachedRoleIds();

    // Also try to load configurations for known roles
    const knownRoles = [
      'admin_yayasan',
      'perwakilan_yayasan',
      'kepala_dapur',
      'ahli_gizi',
      'akuntan',
      'pengawas_pemeliharaan',
    ];

    final allRoleIds = {...cachedRoleIds, ...knownRoles};

    for (final roleId in allRoleIds) {
      final config = await getConfigurationForRole(roleId);
      if (config != null) {
        configurations.add(config);
      }
    }

    return configurations;
  }

  @override
  Future<void> saveConfiguration(DashboardConfiguration configuration) async {
    try {
      final configModel = DashboardConfigurationModel.fromDomain(
        configuration,
        updatedAt: DateTime.now(),
      );

      // Validate before saving
      final validationResult = DashboardConfigurationValidator.validate(
        configModel,
      );
      if (!validationResult.isValid) {
        throw Exception(
          'Configuration validation failed: ${validationResult.summary}',
        );
      }

      // Save as custom configuration
      await _localDataSource.saveCustomConfiguration(configModel);
    } catch (e) {
      throw Exception('Failed to save configuration: $e');
    }
  }

  @override
  Future<void> deleteConfiguration(String roleId) async {
    try {
      await _localDataSource.deleteCustomConfiguration(roleId);
      // Also remove from cache if exists
      await _localDataSource.clearCache();
    } catch (e) {
      throw Exception('Failed to delete configuration: $e');
    }
  }

  @override
  Future<bool> hasConfigurationForRole(String roleId) async {
    try {
      // Check custom configuration first
      final customConfig = await _localDataSource.loadCustomConfiguration(
        roleId,
      );
      if (customConfig != null) return true;

      // Check cached configuration
      final cachedConfig = await _localDataSource.getCachedConfiguration(
        roleId,
      );
      if (cachedConfig != null) return true;

      // Check if asset configuration exists
      final assetConfig = await _localDataSource.loadConfigurationFromAssets(
        roleId,
      );
      return assetConfig != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<DashboardConfiguration> getDefaultConfiguration() async {
    // Return a basic default configuration
    return const DashboardConfiguration(
      roleId: 'default',
      components: [],
      layout: LayoutConfiguration(),
      navigation: NavigationConfiguration(sections: []),
    );
  }

  @override
  Future<bool> validateConfiguration(
    DashboardConfiguration configuration,
  ) async {
    try {
      final configModel = DashboardConfigurationModel.fromDomain(configuration);
      final validationResult = DashboardConfigurationValidator.validate(
        configModel,
      );
      return validationResult.isValid;
    } catch (e) {
      return false;
    }
  }

  /// Clear all cached configurations (useful for development/testing)
  Future<void> clearCache() async {
    await _localDataSource.clearCache();
  }

  /// Reload configuration from assets (bypassing cache)
  Future<DashboardConfiguration?> reloadConfigurationFromAssets(
    String roleId,
  ) async {
    try {
      final configModel = await _localDataSource.loadConfigurationFromAssets(
        roleId,
      );
      if (configModel != null) {
        // Update cache with fresh data
        await _localDataSource.cacheConfiguration(configModel);
        return configModel.toDomain();
      }
      return null;
    } catch (e) {
      _logger.e('Error reloading configuration for role $roleId: $e');
      return null;
    }
  }
}
