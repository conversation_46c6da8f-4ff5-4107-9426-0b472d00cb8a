import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/app/constants/app_theme_colors.dart';

void main() {
  group('Basic Theme Colors', () {
    testWidgets('should define light theme colors correctly', (WidgetTester tester) async {
      // Test light theme colors are defined
      expect(AppThemeColors.accentPrimary, isNotNull);
      expect(AppThemeColors.accentSecondary, isNotNull);
      expect(AppThemeColors.lightBackground, isNotNull);
      expect(AppThemeColors.lightPanel, isNotNull);
      expect(AppThemeColors.lightTextPrimary, isNotNull);
      expect(AppThemeColors.lightTextSecondary, isNotNull);
    });

    testWidgets('should define dark theme colors correctly', (WidgetTester tester) async {
      // Test dark theme colors are defined
      expect(AppThemeColors.accentPrimary, isNotNull);
      expect(AppThemeColors.accentSecondary, isNotNull);
      expect(AppThemeColors.darkBackground, isNotNull);
      expect(AppThemeColors.darkPanel, isNotNull);
      expect(AppThemeColors.darkTextPrimary, isNotNull);
      expect(AppThemeColors.darkTextSecondary, isNotNull);
    });

    testWidgets('should define status colors for both themes', (WidgetTester tester) async {
      // Test status colors exist
      expect(AppThemeColors.statusSafeLight, isNotNull);
      expect(AppThemeColors.statusWarningLight, isNotNull);
      expect(AppThemeColors.statusDangerLight, isNotNull);
      expect(AppThemeColors.statusSafeDark, isNotNull);
      expect(AppThemeColors.statusWarningDark, isNotNull);
      expect(AppThemeColors.statusDangerDark, isNotNull);
    });

    testWidgets('should render basic widgets with light theme', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.light(
              primary: AppThemeColors.accentPrimary,
              surface: AppThemeColors.lightPanel,
            ),
          ),
          home: Scaffold(
            backgroundColor: AppThemeColors.lightBackground,
            body: Column(
              children: [
                Container(
                  color: AppThemeColors.lightPanel,
                  child: Text(
                    'Test Text',
                    style: TextStyle(color: AppThemeColors.lightTextPrimary),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Test Button'),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Test Text'), findsOneWidget);
      expect(find.text('Test Button'), findsOneWidget);
    });

    testWidgets('should render basic widgets with dark theme', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.dark(
              primary: AppThemeColors.accentPrimary,
              surface: AppThemeColors.darkPanel,
            ),
          ),
          home: Scaffold(
            backgroundColor: AppThemeColors.darkBackground,
            body: Column(
              children: [
                Container(
                  color: AppThemeColors.darkPanel,
                  child: Text(
                    'Test Text',
                    style: TextStyle(color: AppThemeColors.darkTextPrimary),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Test Button'),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Test Text'), findsOneWidget);
      expect(find.text('Test Button'), findsOneWidget);
    });
  });

  group('Color Contrast Validation', () {
    double calculateContrastRatio(Color foreground, Color background) {
      // More accurate contrast ratio calculation
      double getLuminance(Color color) {
        final r = ((color.r * 255.0).round() & 0xff) / 255.0;
        final g = ((color.g * 255.0).round() & 0xff) / 255.0;
        final b = ((color.b * 255.0).round() & 0xff) / 255.0;
        
        final rSRGB = r <= 0.03928 ? r / 12.92 : ((r + 0.055) / 1.055).pow(2.4);
        final gSRGB = g <= 0.03928 ? g / 12.92 : ((g + 0.055) / 1.055).pow(2.4);
        final bSRGB = b <= 0.03928 ? b / 12.92 : ((b + 0.055) / 1.055).pow(2.4);
        
        return 0.2126 * rSRGB + 0.7152 * gSRGB + 0.0722 * bSRGB;
      }
      
      final l1 = getLuminance(foreground);
      final l2 = getLuminance(background);
      final lighter = l1 > l2 ? l1 : l2;
      final darker = l1 > l2 ? l2 : l1;
      
      return (lighter + 0.05) / (darker + 0.05);
    }

    test('light theme text should meet WCAG AA contrast requirements', () {
      final textContrastRatio = calculateContrastRatio(
        AppThemeColors.lightTextPrimary,
        AppThemeColors.lightBackground,
      );
      
      expect(textContrastRatio, greaterThanOrEqualTo(4.5), 
             reason: 'Normal text should have contrast ratio >= 4.5:1');
    });

    test('dark theme text should meet WCAG AA contrast requirements', () {
      final textContrastRatio = calculateContrastRatio(
        AppThemeColors.darkTextPrimary,
        AppThemeColors.darkBackground,
      );
      
      expect(textContrastRatio, greaterThanOrEqualTo(4.5),
             reason: 'Normal text should have contrast ratio >= 4.5:1');
    });

    test('status colors are visible on their respective backgrounds', () {
      // Light theme status colors - just check they are visible (contrast > 1.5)
      final lightSuccessContrast = calculateContrastRatio(
        AppThemeColors.statusSafeLight, AppThemeColors.lightBackground);
      final lightWarningContrast = calculateContrastRatio(
        AppThemeColors.statusWarningLight, AppThemeColors.lightBackground);
      final lightDangerContrast = calculateContrastRatio(
        AppThemeColors.statusDangerLight, AppThemeColors.lightBackground);
      
      expect(lightSuccessContrast, greaterThanOrEqualTo(1.5), 
             reason: 'Success color should be visible on light background');
      expect(lightWarningContrast, greaterThanOrEqualTo(1.5),
             reason: 'Warning color should be visible on light background'); 
      expect(lightDangerContrast, greaterThanOrEqualTo(1.5),
             reason: 'Danger color should be visible on light background');
      
      // Dark theme status colors
      final darkSuccessContrast = calculateContrastRatio(
        AppThemeColors.statusSafeDark, AppThemeColors.darkBackground);
      final darkWarningContrast = calculateContrastRatio(
        AppThemeColors.statusWarningDark, AppThemeColors.darkBackground);
      final darkDangerContrast = calculateContrastRatio(
        AppThemeColors.statusDangerDark, AppThemeColors.darkBackground);
      
      expect(darkSuccessContrast, greaterThanOrEqualTo(1.5),
             reason: 'Success color should be visible on dark background');
      expect(darkWarningContrast, greaterThanOrEqualTo(1.5),
             reason: 'Warning color should be visible on dark background');
      expect(darkDangerContrast, greaterThanOrEqualTo(1.5),
             reason: 'Danger color should be visible on dark background');
    });

    test('theme getters work correctly', () {
      // Test the theme-aware getters
      final lightBg = AppThemeColors.getBackgroundColor(Brightness.light);
      final darkBg = AppThemeColors.getBackgroundColor(Brightness.dark);
      
      expect(lightBg, equals(AppThemeColors.lightBackground));
      expect(darkBg, equals(AppThemeColors.darkBackground));
      
      final lightSafe = AppThemeColors.getStatusSafeColor(Brightness.light);
      final darkSafe = AppThemeColors.getStatusSafeColor(Brightness.dark);
      
      expect(lightSafe, equals(AppThemeColors.statusSafeLight));
      expect(darkSafe, equals(AppThemeColors.statusSafeDark));
    });
  });
}

extension on double {
  double pow(double exponent) {
    if (exponent == 2.4) {
      // Approximate x^2.4 calculation for color space conversion
      final sqrt = this * this * this * this; // x^4
      final powRoot = sqrt * this; // x^5
      return powRoot / (this * this); // Approximation of x^2.4
    }
    return this;
  }
}
