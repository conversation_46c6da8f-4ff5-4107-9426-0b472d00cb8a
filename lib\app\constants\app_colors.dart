import 'package:flutter/material.dart';

/// Defines the color palette for the SOD-MBG application.
/// Based on the YellowBlueSkyHappy color scheme with extended Fluent UI colors.
class AppColors {
  // ===== PRIMARY COLORS =====
  static const Color primary = Color(0xFF749BC2);
  static const Color primaryLight = Color(0xFF8BB2D6);
  static const Color primaryDark = Color(0xFF5C82A8);
  static const Color secondary = Color(0xFFFFFBDE);
  static const Color accent = Color(0xFFF6F4EB);

  // ===== TEXT COLORS =====
  static const Color textPrimary = Color(
    0xFF1F2937,
  ); // Lebih gelap untuk kontras yang baik
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textTertiary = Color(0xFF9CA3AF);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFF1F2937);
  static const Color textDisabled = Color(0xFF9CA3AF);
  static const Color textOnDark = Color(
    0xFFF1F1F1,
  ); // Untuk teks di background gelap
  static const Color textSecondaryOnDark = Color(
    0xFFCCCCCC,
  ); // Teks sekunder di background gelap

  // ===== EXTENDED COLORS =====
  static const Color secondaryLight = Color(0xFFFFFDE8);
  static const Color secondaryDark = Color(0xFFF7F3C7);
  static const Color neutralWhite = Color(0xFFFFFFFF);
  static const Color kitchenClean = Color(0xFFEEF2F5);
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFFE8F5E8);
  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFF3E0);
  static const Color adminYayasanColor = Color(0xFF8B5CF6);

  // ===== BACKGROUND COLORS =====
  static const Color background = Color(0xFFFFFFFF);
  static const Color backgroundPrimary = Color(0xFFFFFFFF);
  static const Color backgroundSecondary = Color(
    0xFFF5F5F5,
  ); // Lebih netral untuk padding/separator
  static const Color backgroundTertiary = Color(
    0xFFEAEAEA,
  ); // Untuk konten bagian bawah
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF8FAFC);
  static const Color dashboardDark = Color(
    0xFF1E1E2E,
  ); // Background gelap yang nyaman untuk mata

  // ===== SEMANTIC COLORS =====
  static const Color successGreen = Color(0xFF4CAF50);
  static const Color warningOrange = Color(
    0xFFFFA500,
  ); // Lebih konsisten untuk status "Berlangsung"
  static const Color warningYellow = Color(0xFFFBBF24);
  static const Color errorRed = Color(0xFFF44336);
  static const Color dangerRed = Color(0xFFDC2626);
  static const Color infoBlue = Color(0xFF2196F3); // Untuk status "Menunggu"
  static const Color info = Color(0xFF2196F3); // Alias for infoBlue

  // ===== STATUS COLORS - Konsisten untuk scan cepat =====
  static const Color statusCompleted = Color(
    0xFF4CAF50,
  ); // Hijau untuk "Selesai"
  static const Color statusInProgress = Color(
    0xFFFFA500,
  ); // Oranye untuk "Berlangsung"
  static const Color statusPending = Color(0xFF2196F3); // Biru untuk "Menunggu"
  static const Color statusCancelled = Color(
    0xFFF44336,
  ); // Merah untuk "Dibatalkan"

  // ===== NEUTRAL COLORS =====
  static const Color neutralGray50 = Color(0xFFF9FAFB);
  static const Color neutralGray100 = Color(0xFFF3F4F6);
  static const Color neutralGray200 = Color(0xFFE5E7EB);
  static const Color neutralGray300 = Color(0xFFD1D5DB);
  static const Color neutralGray400 = Color(0xFF9CA3AF);
  static const Color neutralGray500 = Color(0xFF6B7280);
  static const Color neutralGray600 = Color(0xFF4B5563);
  static const Color neutralGray700 = Color(0xFF374151);
  static const Color neutralGray800 = Color(0xFF1F2937);
  static const Color neutralGray900 = Color(0xFF111827);

  // ===== GREY ALIASES FOR CONVENIENCE =====
  static const Color grey100 = neutralGray100;
  static const Color grey200 = neutralGray200;
  static const Color grey300 = neutralGray300;
  static const Color grey400 = neutralGray400;
  static const Color grey500 = neutralGray500;
  static const Color grey600 = neutralGray600;

  // ===== BORDER COLORS =====
  static const Color borderPrimary = Color(0xFFE5E7EB);
  static const Color borderSecondary = Color(0xFFD1D5DB);
  static const Color borderAccent = Color(0xFF749BC2);
  static const Color borderFocus = Color(0xFF2563EB);

  // ===== FLUENT UI SPECIFIC COLORS =====
  static const Color fluentAccent = Color(0xFF0078D4);
  static const Color fluentAccentLight = Color(0xFF106EBE);
  static const Color fluentAccentDark = Color(0xFF005A9E);

  // ===== ROLE-BASED COLORS =====
  static const Color adminRole = Color(0xFF8B5CF6);
  static const Color managerRole = Color(0xFFEF4444);
  static const Color userRole = Color(0xFF10B981);
  static const Color viewerRole = Color(0xFF6B7280);

  // ===== KITCHEN SPECIFIC COLORS =====
  static const Color kitchenPrimary = Color(0xFF059669);
  static const Color kitchenSecondary = Color(0xFFFBBF24);
  static const Color kitchenDanger = Color(0xFFEF4444);
  static const Color kitchenWarning = Color(0xFFF59E0B);
  static const Color kitchenInfo = Color(0xFF3B82F6);

  // ===== SKELETON LOADING COLORS =====
  static const Color skeletonBase = Color(0xFFE5E7EB);
  static const Color skeletonHighlight = Color(0xFFF3F4F6);

  // ===== OPACITY VARIANTS =====
  static Color primaryWithOpacity(double opacity) =>
      primary.withValues(alpha: opacity);
  static Color secondaryWithOpacity(double opacity) =>
      secondary.withValues(alpha: opacity);
  static Color accentWithOpacity(double opacity) =>
      accent.withValues(alpha: opacity);
  static Color textWithOpacity(double opacity) =>
      textPrimary.withValues(alpha: opacity);
  static Color backgroundWithOpacity(double opacity) =>
      background.withValues(alpha: opacity);

  // ===== THEME VARIANTS =====
  static Color get primaryContainer => primaryLight.withValues(alpha: 0.1);
  static Color get secondaryContainer => secondary.withValues(alpha: 0.1);
  static Color get errorContainer => errorRed.withValues(alpha: 0.1);
  static Color get warningContainer => warningOrange.withValues(alpha: 0.1);
  static Color get successContainer => successGreen.withValues(alpha: 0.1);
  static Color get infoContainer => infoBlue.withValues(alpha: 0.1);

  // ===== CHART COLORS - IDEAL PALETTE =====
  static const List<Color> chartColors = [
    Color(0xFF749BC2), // Primary blue - untuk primary actions
    Color(0xFF4CAF50), // Success green - untuk completed/success
    Color(0xFF2196F3), // Info blue - untuk informational items
    Color(0xFFFFA500), // Orange - untuk in-progress/warning
    Color(0xFF9C27B0), // Purple - untuk special/admin items
    Color(0xFF00BCD4), // Cyan - untuk delivery/logistics
    Color(0xFF8BC34A), // Light green - untuk inventory/stock
    Color(0xFFFF5722), // Deep orange - untuk urgent items
    Color(0xFF795548), // Brown - untuk secondary actions
    Color(0xFF607D8B), // Blue grey - untuk neutral items
  ];

  // ===== ACTIVITY SPECIFIC COLORS - SEMANTIC MAPPING =====
  static const Color activityStock = Color(
    0xFF4CAF50,
  ); // Green untuk stock/inventory
  static const Color activityQuality = Color(
    0xFF2196F3,
  ); // Blue untuk quality control
  static const Color activityDelivery = Color(
    0xFF00BCD4,
  ); // Cyan untuk delivery
  static const Color activityFinance = Color(
    0xFF9C27B0,
  ); // Purple untuk finance
  static const Color activityUrgent = Color(
    0xFFFF5722,
  ); // Deep orange untuk urgent

  // ===== GRADIENT COLORS =====
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, accent],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient kitchenGradient = LinearGradient(
    colors: [kitchenPrimary, kitchenSecondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // ===== HELPER METHODS =====

  /// Get color based on role
  static Color getRoleColor(String role) {
    switch (role) {
      case 'admin_yayasan':
        return adminRole;
      case 'perwakilan_yayasan':
        return infoBlue;
      case 'kepala_dapur':
        return kitchenPrimary;
      case 'ahli_gizi':
        return successGreen;
      case 'akuntan':
        return warningOrange;
      case 'pengawas_pemeliharaan':
        return neutralGray600;
      default:
        return primary;
    }
  }

  /// Get semantic color based on type
  static Color getSemanticColor(String type) {
    switch (type) {
      case 'success':
      case 'completed':
      case 'selesai':
        return statusCompleted;
      case 'warning':
      case 'inprogress':
      case 'berlangsung':
        return statusInProgress;
      case 'error':
        return errorRed;
      case 'danger':
        return dangerRed;
      case 'info':
      case 'pending':
      case 'menunggu':
        return statusPending;
      case 'cancelled':
      case 'dibatalkan':
        return statusCancelled;
      default:
        return primary;
    }
  }

  /// Get status color based on status text (for consistent status indicators)
  static Color getStatusColor(String status) {
    final lowerStatus = status.toLowerCase();
    if (lowerStatus.contains('selesai') ||
        lowerStatus.contains('completed') ||
        lowerStatus.contains('success')) {
      return statusCompleted;
    } else if (lowerStatus.contains('berlangsung') ||
        lowerStatus.contains('progress') ||
        lowerStatus.contains('proses')) {
      return statusInProgress;
    } else if (lowerStatus.contains('menunggu') ||
        lowerStatus.contains('pending') ||
        lowerStatus.contains('waiting')) {
      return statusPending;
    } else if (lowerStatus.contains('dibatalkan') ||
        lowerStatus.contains('cancelled') ||
        lowerStatus.contains('batal')) {
      return statusCancelled;
    } else {
      return neutralGray500;
    }
  }

  /// Get highlight background color - Menggunakan #FFFBDE untuk card penting
  static Color getHighlightBackground(String priority) {
    switch (priority) {
      case 'high':
      case 'important':
      case 'urgent':
        return secondary; // #FFFBDE - untuk highlight
      case 'medium':
        return secondaryLight;
      default:
        return surfaceColor;
    }
  }

  /// Get activity color based on activity type - Semantic mapping untuk aktivitas
  static Color getActivityColor(String activityType) {
    switch (activityType.toLowerCase()) {
      case 'stock':
      case 'inventory':
      case 'penerimaan':
      case 'bahan_baku':
        return activityStock;
      case 'qc':
      case 'quality':
      case 'control':
      case 'passed':
        return activityQuality;
      case 'delivery':
      case 'pengiriman':
      case 'distribusi':
      case 'logistics':
        return activityDelivery;
      case 'finance':
      case 'keuangan':
      case 'budget':
      case 'payment':
        return activityFinance;
      case 'urgent':
      case 'emergency':
      case 'critical':
        return activityUrgent;
      default:
        return primary;
    }
  }

  /// Get contrast color for text on background
  static Color getContrastColor(Color backgroundColor) {
    // Calculate luminance
    double luminance = backgroundColor.computeLuminance();

    // Return white for dark backgrounds, dark for light backgrounds
    return luminance > 0.5 ? textPrimary : textOnPrimary;
  }

  /// Get hover color for interactive elements
  static Color getHoverColor(Color baseColor) {
    return baseColor.withValues(alpha: 0.8);
  }

  /// Get pressed color for interactive elements
  static Color getPressedColor(Color baseColor) {
    return baseColor.withValues(alpha: 0.6);
  }

  /// Get disabled color for interactive elements
  static Color getDisabledColor(Color baseColor) {
    return neutralGray300;
  }

  /// Get focus color for interactive elements
  static Color getFocusColor(Color baseColor) {
    return baseColor.withValues(alpha: 0.2);
  }

  // ===== BACKWARD COMPATIBILITY ALIASES =====
  // These aliases maintain compatibility with existing code while
  // encouraging migration to the new theme-aware system

  /// @deprecated Use context.accentPrimary instead
  @Deprecated('Use context.accentPrimary instead for theme-aware colors')
  static Color get themePrimary => primary;

  /// @deprecated Use context.textPrimary instead
  @Deprecated('Use context.textPrimary instead for theme-aware colors')
  static Color get themeTextPrimary => textPrimary;

  /// @deprecated Use context.textSecondary instead
  @Deprecated('Use context.textSecondary instead for theme-aware colors')
  static Color get themeTextSecondary => textSecondary;

  /// @deprecated Use context.backgroundColor instead
  @Deprecated('Use context.backgroundColor instead for theme-aware colors')
  static Color get themeBackground => background;

  /// @deprecated Use context.panelColor instead
  @Deprecated('Use context.panelColor instead for theme-aware colors')
  static Color get themePanel => surfaceColor;

  /// @deprecated Use context.getStatusColor('success') instead
  @Deprecated(
    'Use context.getStatusColor("success") instead for theme-aware colors',
  )
  static Color get themeSuccess => successGreen;

  /// @deprecated Use context.getStatusColor('warning') instead
  @Deprecated(
    'Use context.getStatusColor("warning") instead for theme-aware colors',
  )
  static Color get themeWarning => warningOrange;

  /// @deprecated Use context.getStatusColor('danger') instead
  @Deprecated(
    'Use context.getStatusColor("danger") instead for theme-aware colors',
  )
  static Color get themeDanger => errorRed;

  /// @deprecated Use context.dividerColor instead
  @Deprecated('Use context.dividerColor instead for theme-aware colors')
  static Color get themeDivider => borderPrimary;
}
