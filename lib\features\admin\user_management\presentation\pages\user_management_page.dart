import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../app/constants/app_colors.dart';
import '../../../../../app/constants/app_spacing.dart';
import '../../../../../app/constants/app_typography.dart';
import '../../../../../app/widgets/stats_card_widget.dart';
import '../../../../../app/widgets/responsive_layout.dart';
import '../../../../../app/widgets/form_dialog.dart';
import '../../../../../core/utils/connection_manager.dart';
import '../../../../../app/services/statistics_service.dart';
import '../../data/repositories/mock_user_management_repository.dart';
import '../../domain/models/user_management.dart';
import '../cubit/user_management_cubit.dart';
import '../cubit/user_management_state.dart';
import '../widgets/user_form_widget.dart';
import '../widgets/user_table_widget.dart';

/// Provider widget untuk UserManagementPage
class UserManagementPageProvider extends StatelessWidget {
  const UserManagementPageProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) => UserManagementCubit(
            MockUserManagementRepository(),
            ConnectionManager.instance,
          ),
      child: const UserManagementPage(),
    );
  }
}

/// Halaman utama untuk manajemen pengguna
class UserManagementPage extends StatefulWidget {
  const UserManagementPage({super.key});

  @override
  State<UserManagementPage> createState() => _UserManagementPageState();
}

class _UserManagementPageState extends State<UserManagementPage> {
  final TextEditingController _searchController = TextEditingController();
  UserRole? _selectedRoleFilter;
  UserStatus? _selectedStatusFilter;

  @override
  void initState() {
    super.initState();
    // Load initial data
    context.read<UserManagementCubit>().loadUsers();
    _initializeStatistics(); // Initialize statistics service
  }

  Future<void> _initializeStatistics() async {
    try {
      await StatisticsService.instance.initialize();
    } catch (e) {
      // Handle initialization error silently - the UI will show error states
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Handle state changes for export operations and other notifications
  void _handleStateChanges(BuildContext context, UserManagementState state) {
    // Handle export states
    if (state is UserManagementExporting) {
      _showExportProgressDialog(context, state.format);
    } else if (state is UserManagementExportSuccess) {
      Navigator.of(context).pop(); // Close progress dialog
      _showExportSuccessDialog(context, state);
    } else if (state is UserManagementExportError) {
      Navigator.of(context).pop(); // Close progress dialog
      _showExportErrorDialog(context, state);
    }
    // Handle other operation states
    else if (state is UserManagementCreateSuccess) {
      _showSuccessMessage('Pengguna berhasil ditambahkan');
    } else if (state is UserManagementUpdateSuccess) {
      _showSuccessMessage('Data pengguna berhasil diperbarui');
    } else if (state is UserManagementDeleteSuccess) {
      _showSuccessMessage('Pengguna berhasil dihapus');
    }
    // Handle error states
    else if (state is UserManagementCreateError) {
      _showErrorMessage('Gagal menambahkan pengguna: ${state.message}');
    } else if (state is UserManagementUpdateError) {
      _showErrorMessage('Gagal memperbarui pengguna: ${state.message}');
    } else if (state is UserManagementDeleteError) {
      _showErrorMessage('Gagal menghapus pengguna: ${state.message}');
    }
  }

  /// Show export progress dialog
  void _showExportProgressDialog(BuildContext context, String format) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => ContentDialog(
            title: Text('Mengekspor Data $format'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ProgressRing(),
                SizedBox(height: 16),
                Text('Sedang memproses data untuk ekspor...'),
                SizedBox(height: 8),
                Text(
                  'Mohon tunggu, proses ini mungkin memakan waktu beberapa saat.',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
    );
  }

  /// Show export success dialog
  void _showExportSuccessDialog(
    BuildContext context,
    UserManagementExportSuccess state,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Export Berhasil'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(FluentIcons.check_mark, color: Colors.green, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Data berhasil diekspor ke format ${state.format}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(FluentIcons.document, size: 16),
                          const SizedBox(width: 8),
                          const Text(
                            'File:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        state.filePath,
                        style: const TextStyle(
                          fontSize: 12,
                          fontFamily: 'monospace',
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          const Icon(FluentIcons.number, size: 16),
                          const SizedBox(width: 8),
                          Text(
                            'Total Record: ${state.recordCount}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                const Text(
                  'File telah disimpan di folder Downloads perangkat Anda.',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            actions: [
              FilledButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  /// Show export error dialog
  void _showExportErrorDialog(
    BuildContext context,
    UserManagementExportError state,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Export Gagal'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(FluentIcons.error, color: Colors.red, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Gagal mengekspor data ke format ${state.format}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Detail Error:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(state.message, style: const TextStyle(fontSize: 12)),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                const Text(
                  'Silakan coba lagi atau hubungi administrator jika masalah berlanjut.',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            actions: [
              Button(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Tutup'),
              ),
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Retry export based on format
                  if (state.format.toLowerCase() == 'csv') {
                    context.read<UserManagementCubit>().exportToCSV();
                  } else {
                    context.read<UserManagementCubit>().exportToExcel();
                  }
                },
                child: const Text('Coba Lagi'),
              ),
            ],
          ),
    );
  }

  /// Show success message
  void _showSuccessMessage(String message) {
    // This would typically use a toast/snackbar service
    // For now, we'll use a simple dialog
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Berhasil'),
            content: Text(message),
            actions: [
              FilledButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  /// Show error message
  void _showErrorMessage(String message) {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Error'),
            content: Text(message),
            actions: [
              FilledButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showAddUserDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return FormDialog(
          config: const FormDialogConfig(
            title: 'Tambah Pengguna Baru',
            icon: FluentIcons.add,
          ),
          child: UserFormWidget(
            onSave: (user) async {
              await context.read<UserManagementCubit>().createUser(user);
              Navigator.pop(context);
            },
            onCancel: () => Navigator.pop(context),
            availableSppgOptions: _getAvailableSppgOptions(),
          ),
        );
      },
    );
  }

  void _showEditUserDialog(UserManagement user) {
    showDialog(
      context: context,
      builder: (context) {
        return FormDialog(
          config: FormDialogConfig(
            title: 'Edit Pengguna: ${user.nama}',
            icon: FluentIcons.edit,
          ),
          child: UserFormWidget(
            initialData: user,
            onSave: (updatedUser) async {
              await context.read<UserManagementCubit>().updateUser(updatedUser);
              Navigator.pop(context);
            },
            onCancel: () => Navigator.pop(context),
            availableSppgOptions: _getAvailableSppgOptions(),
          ),
        );
      },
    );
  }

  List<SppgOption> _getAvailableSppgOptions() {
    // TODO: Replace with actual data from SppgCubit
    // This should fetch SPPG data for assignment
    return [
      SppgOption(
        id: 'sppg_001',
        nama: 'SPPG Melati',
        alamat: 'Jl. Melati No. 123, Jakarta Selatan',
      ),
      SppgOption(
        id: 'sppg_002',
        nama: 'SPPG Mawar',
        alamat: 'Jl. Mawar No. 456, Jakarta Timur',
      ),
      SppgOption(
        id: 'sppg_003',
        nama: 'SPPG Kenanga',
        alamat: 'Jl. Kenanga No. 789, Jakarta Barat',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserManagementCubit, UserManagementState>(
      listener: _handleStateChanges,
      child: ScaffoldPage(
        header: _buildHeader(),
        content: ResponsiveLayout(
          mobile: _buildMobileLayout(),
          tablet: _buildTabletLayout(),
          desktop: _buildDesktopLayout(),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        children: [
          _buildSearchAndFilters(),
          const SizedBox(height: AppSpacing.md),
          Expanded(child: _buildUserTable()),
        ],
      ),
    );
  }

  Widget _buildTabletLayout() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          _buildSearchAndFilters(),
          const SizedBox(height: AppSpacing.md),
          Expanded(child: _buildUserTable()),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Column(
        children: [
          _buildStatsCards(),
          const SizedBox(height: AppSpacing.lg),
          _buildSearchAndFilters(),
          const SizedBox(height: AppSpacing.md),
          Expanded(child: _buildUserTable()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return PageHeader(
      title: const Text('Manajemen Pengguna'),
      commandBar: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Button(
            onPressed: () {
              context.read<UserManagementCubit>().refreshUsers();
            },
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(FluentIcons.refresh, size: 16),
                SizedBox(width: 8),
                Text('Refresh'),
              ],
            ),
          ),
          const SizedBox(width: 8),
          FilledButton(
            onPressed: _showAddUserDialog,
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(FluentIcons.add, size: 16),
                SizedBox(width: 8),
                Text('Tambah Pengguna'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCards() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return StreamBuilder<Map<String, dynamic>>(
          stream: _getUserStatsStream(),
          builder: (context, snapshot) {
            if (snapshot.hasError) {
              return _buildStatsError(snapshot.error.toString());
            }

            final stats = snapshot.data ?? {};
            final isLoading = !snapshot.hasData;

            return _buildStatsGrid(stats, isLoading);
          },
        );
      },
    );
  }

  Stream<Map<String, dynamic>> _getUserStatsStream() {
    return StatisticsService.instance.userAnalyticsStream.map((analytics) {
      final trends = StatisticsService.instance.getTrendIndicators();

      return {
        'total': {
          'value': analytics.total.toString(),
          'trend': trends['userTotal'],
        },
        'active': {
          'value': analytics.active.toString(),
          'trend': trends['userActive'],
        },
        'pending': {
          'value': analytics.pending.toString(),
          'trend': trends['userPending'],
        },
        'suspended': {
          'value': analytics.suspended.toString(),
          'trend': trends['userSuspended'],
        },
      };
    });
  }

  Widget _buildStatsGrid(Map<String, dynamic> stats, bool isLoading) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      child: Row(
        children: [
          Expanded(
            child: StatsCardWidget(
              title: 'Total Pengguna',
              value: stats['total']?['value'] ?? '0',
              icon: FluentIcons.people,
              color: AppColors.primary,
              isLoading: isLoading,
              onTap: () => _onStatsCardTap('total'),
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: StatsCardWidget(
              title: 'Pengguna Aktif',
              value: stats['active']?['value'] ?? '0',
              icon: FluentIcons.check_mark,
              color: AppColors.successGreen,
              isLoading: isLoading,
              onTap: () => _onStatsCardTap('active'),
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: StatsCardWidget(
              title: 'Pengguna Pending',
              value: stats['pending']?['value'] ?? '0',
              icon: FluentIcons.clock,
              color: AppColors.warningOrange,
              isLoading: isLoading,
              onTap: () => _onStatsCardTap('pending'),
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: StatsCardWidget(
              title: 'Pengguna Suspended',
              value: stats['suspended']?['value'] ?? '0',
              icon: FluentIcons.blocked,
              color: AppColors.errorRed,
              isLoading: isLoading,
              onTap: () => _onStatsCardTap('suspended'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsError(String error) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Row(
            children: [
              Icon(FluentIcons.error, color: AppColors.errorRed, size: 24),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Gagal memuat statistik',
                      style: AppTypography.titleMedium.copyWith(
                        color: AppColors.errorRed,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Terjadi kesalahan saat memuat data statistik pengguna',
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Button(
                onPressed:
                    () => StatisticsService.instance.refreshUserAnalytics(),
                child: const Text('Coba Lagi'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onStatsCardTap(String cardType) {
    // Handle stats card tap - filter the table based on the card type
    switch (cardType) {
      case 'total':
        _clearFilters();
        break;
      case 'active':
        _onStatusFilterChanged(UserStatus.active);
        break;
      case 'pending':
        _onStatusFilterChanged(UserStatus.pending);
        break;
      case 'suspended':
        _onStatusFilterChanged(UserStatus.suspended);
        break;
    }
  }

  void _clearFilters() {
    _searchController.clear();
    setState(() {
      _selectedRoleFilter = null;
      _selectedStatusFilter = null;
    });
    context.read<UserManagementCubit>().clearFilters();
  }

  void _onStatusFilterChanged(UserStatus status) {
    setState(() {
      _selectedStatusFilter = status;
    });
    context.read<UserManagementCubit>().filterByStatus(status);
  }

  Widget _buildSearchAndFilters() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextBox(
                    controller: _searchController,
                    placeholder: 'Cari nama, email, atau SPPG...',
                    prefix: const Icon(FluentIcons.search),
                    onChanged: (value) {
                      context.read<UserManagementCubit>().searchUsers(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ComboBox<UserRole>(
                    placeholder: const Text('Semua Peran'),
                    value: _selectedRoleFilter,
                    items:
                        UserRole.values.map((role) {
                          return ComboBoxItem<UserRole>(
                            value: role,
                            child: Text(role.displayName),
                          );
                        }).toList(),
                    onChanged: (role) {
                      setState(() => _selectedRoleFilter = role);
                      context.read<UserManagementCubit>().filterByRole(role);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ComboBox<UserStatus>(
                    placeholder: const Text('Semua Status'),
                    value: _selectedStatusFilter,
                    items:
                        UserStatus.values.map((status) {
                          return ComboBoxItem<UserStatus>(
                            value: status,
                            child: Text(status.displayName),
                          );
                        }).toList(),
                    onChanged: (status) {
                      setState(() => _selectedStatusFilter = status);
                      context.read<UserManagementCubit>().filterByStatus(
                        status,
                      );
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Button(
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _selectedRoleFilter = null;
                      _selectedStatusFilter = null;
                    });
                    context.read<UserManagementCubit>().clearFilters();
                  },
                  child: const Text('Reset Filter'),
                ),
                const Spacer(),
                Button(
                  onPressed: () {
                    context.read<UserManagementCubit>().exportToCSV();
                  },
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(FluentIcons.download, size: 16),
                      SizedBox(width: 8),
                      Text('Export CSV'),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Button(
                  onPressed: () {
                    context.read<UserManagementCubit>().exportToExcel();
                  },
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(FluentIcons.excel_logo, size: 16),
                      SizedBox(width: 8),
                      Text('Export Excel'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserTable() {
    return BlocBuilder<UserManagementCubit, UserManagementState>(
      builder: (context, state) {
        if (state is UserManagementLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ProgressRing(),
                SizedBox(height: 16),
                Text('Memuat data pengguna...'),
              ],
            ),
          );
        }

        if (state is UserManagementError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(FluentIcons.error, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error: ${state.message}'),
                const SizedBox(height: 16),
                Button(
                  onPressed: () {
                    context.read<UserManagementCubit>().loadUsers();
                  },
                  child: const Text('Coba Lagi'),
                ),
              ],
            ),
          );
        }

        if (state is UserManagementLoaded) {
          final users = state.filteredUsers;

          return UserTableWidget(
            users: users,
            onEdit: (userId) {
              final user = users.firstWhere((u) => u.id == userId);
              _showEditUserDialog(user);
            },
            onDelete: (userId) {
              final user = users.firstWhere((u) => u.id == userId);
              _showDeleteDialog(user);
            },
            onResetPassword: (userId) {
              context.read<UserManagementCubit>().resetPassword(userId);
            },
            onStatusChange: (userId, newStatus) {
              context.read<UserManagementCubit>().updateUserStatus(
                userId,
                newStatus,
              );
            },
            isLoading: state is UserManagementLoading,
            bulkActions: [
              BulkAction(
                id: 'activate',
                label: 'Aktifkan',
                icon: FluentIcons.play,
                color: AppColors.successGreen,
                onExecute: (selectedIds) {
                  for (final id in selectedIds) {
                    context.read<UserManagementCubit>().updateUserStatus(
                      id,
                      UserStatus.active,
                    );
                  }
                },
              ),
              BulkAction(
                id: 'suspend',
                label: 'Suspend',
                icon: FluentIcons.blocked,
                color: AppColors.warningOrange,
                onExecute: (selectedIds) {
                  for (final id in selectedIds) {
                    context.read<UserManagementCubit>().updateUserStatus(
                      id,
                      UserStatus.suspended,
                    );
                  }
                },
              ),
              BulkAction(
                id: 'reset_password',
                label: 'Reset Password',
                icon: FluentIcons.password_field,
                color: AppColors.infoBlue,
                onExecute: (selectedIds) {
                  for (final id in selectedIds) {
                    context.read<UserManagementCubit>().resetPassword(id);
                  }
                },
              ),
            ],
            emptyMessage: 'Belum ada pengguna yang terdaftar',
            emptySubtitle:
                'Klik tombol "Tambah Pengguna" untuk menambahkan pengguna pertama',
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  void _showDeleteDialog(UserManagement user) {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Hapus Pengguna'),
            content: Text(
              'Apakah Anda yakin ingin menghapus pengguna "${user.nama}"? '
              'Aksi ini akan menonaktifkan akun pengguna.',
            ),
            actions: [
              Button(
                child: const Text('Batal'),
                onPressed: () => Navigator.pop(context),
              ),
              FilledButton(
                style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(AppColors.errorRed),
                ),
                child: const Text('Hapus'),
                onPressed: () {
                  Navigator.pop(context);
                  context.read<UserManagementCubit>().deleteUser(user.id);
                },
              ),
            ],
          ),
    );
  }

  Widget _buildTableHeader() { // ignore: unused_element
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: FluentTheme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: FluentTheme.of(context).inactiveColor.withValues(alpha: 0.1),
          ),
        ),
      ),
      child: const Row(
        children: [
          Expanded(flex: 2, child: Text('Nama')),
          Expanded(flex: 2, child: Text('Email')),
          Expanded(flex: 1, child: Text('Peran')),
          Expanded(flex: 2, child: Text('SPPG')),
          Expanded(flex: 1, child: Text('Status')),
          Expanded(flex: 1, child: Text('Last Login')),
          SizedBox(width: 60, child: Text('Aksi')),
        ],
      ),
    );
  }

  Widget _buildTableRow(UserManagement user, int index) { // ignore: unused_element
    final isEven = index % 2 == 0;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            isEven
                ? FluentTheme.of(context).scaffoldBackgroundColor
                : FluentTheme.of(context).cardColor.withValues(alpha: 0.5),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: _getUserStatusColor(user.status),
                  child: Text(
                    user.nama.isNotEmpty ? user.nama[0].toUpperCase() : 'U',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.nama,
                        style: FluentTheme.of(context).typography.bodyStrong,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (user.nip != null && user.nip!.isNotEmpty)
                        Text(
                          'NIP: ${user.nip}',
                          style: FluentTheme.of(context).typography.caption,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(user.email, overflow: TextOverflow.ellipsis),
          ),
          Expanded(
            flex: 1,
            child: Text(user.role.displayName, overflow: TextOverflow.ellipsis),
          ),
          Expanded(
            flex: 2,
            child: Text(user.sppgName ?? '-', overflow: TextOverflow.ellipsis),
          ),
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getUserStatusColor(user.status).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                user.status.displayName,
                style: TextStyle(
                  color: _getUserStatusColor(user.status),
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              user.lastLoginDisplay,
              style: FluentTheme.of(context).typography.caption,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(
            width: 60,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(FluentIcons.edit),
                  onPressed: () {
                    // TODO: Show edit dialog
                    // Logger().d('Edit user: ${user.nama}');
                  },
                ),
                IconButton(
                  icon: const Icon(FluentIcons.delete),
                  onPressed: () {
                    _showDeleteDialog(user);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getUserStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return Colors.green;
      case UserStatus.inactive:
        return Colors.grey;
      case UserStatus.suspended:
        return Colors.red;
      case UserStatus.pending:
        return Colors.orange;
    }
  }
}
