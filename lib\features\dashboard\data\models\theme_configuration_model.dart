import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:fluent_ui/fluent_ui.dart';
import '../../domain/entities/entities.dart';

part 'theme_configuration_model.g.dart';

/// Data model for theme configuration with JSON serialization
@JsonSerializable(explicitToJson: true)
class ThemeConfigurationModel extends Equatable {
  /// Primary accent color as hex string
  @Json<PERSON>ey(name: 'accent_color_hex')
  final String? accentColorHex;
  
  /// Background color for cards as hex string
  @J<PERSON><PERSON>ey(name: 'card_color_hex')
  final String? cardColorHex;
  
  /// Text theme customizations
  @JsonKey(name: 'text_styles')
  final Map<String, TextStyleModel>? textStyles;

  const ThemeConfigurationModel({
    this.accentColorHex,
    this.cardColorHex,
    this.textStyles,
  });

  /// Create from JSON
  factory ThemeConfigurationModel.fromJson(Map<String, dynamic> json) =>
      _$ThemeConfigurationModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$ThemeConfigurationModelToJson(this);

  /// Convert to domain entity
  ThemeConfiguration toDomain() {
    Color? accentColor;
    if (accentColorHex != null) {
      accentColor = Color(int.parse(accentColorHex!.replaceFirst('#', '0xFF')));
    }

    Color? cardColor;
    if (cardColorHex != null) {
      cardColor = Color(int.parse(cardColorHex!.replaceFirst('#', '0xFF')));
    }

    Map<String, TextStyle>? textStyleMap;
    if (textStyles != null) {
      textStyleMap = textStyles!.map(
        (key, value) => MapEntry(key, value.toDomain()),
      );
    }

    return ThemeConfiguration(
      accentColor: accentColor,
      cardColor: cardColor,
      textStyles: textStyleMap,
    );
  }

  /// Create from domain entity
  factory ThemeConfigurationModel.fromDomain(ThemeConfiguration domain) {
    Map<String, TextStyleModel>? textStyleMap;
    if (domain.textStyles != null) {
      textStyleMap = domain.textStyles!.map(
        (key, value) => MapEntry(key, TextStyleModel.fromDomain(value)),
      );
    }

    return ThemeConfigurationModel(
      accentColorHex: domain.accentColor != null
          ? '#${domain.accentColor!.toARGB32().toRadixString(16).padLeft(8, '0')}'
          : null,
      cardColorHex: domain.cardColor != null
          ? '#${domain.cardColor!.toARGB32().toRadixString(16).padLeft(8, '0')}'
          : null,
      textStyles: textStyleMap,
    );
  }

  @override
  List<Object?> get props => [accentColorHex, cardColorHex, textStyles];
}

/// Data model for text style serialization
@JsonSerializable()
class TextStyleModel extends Equatable {
  /// Font size
  @JsonKey(name: 'font_size')
  final double? fontSize;
  
  /// Font weight (as integer)
  @JsonKey(name: 'font_weight', fromJson: _fontWeightFromJson, toJson: _fontWeightToJson)
  final FontWeight? fontWeight;
  
  /// Font family
  @JsonKey(name: 'font_family')
  final String? fontFamily;
  
  /// Text color as hex string
  @JsonKey(name: 'color_hex')
  final String? colorHex;
  
  /// Letter spacing
  @JsonKey(name: 'letter_spacing')
  final double? letterSpacing;
  
  /// Line height
  @JsonKey(name: 'height')
  final double? height;

  const TextStyleModel({
    this.fontSize,
    this.fontWeight,
    this.fontFamily,
    this.colorHex,
    this.letterSpacing,
    this.height,
  });

  /// Create from JSON
  factory TextStyleModel.fromJson(Map<String, dynamic> json) =>
      _$TextStyleModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$TextStyleModelToJson(this);

  /// Convert to TextStyle
  TextStyle toDomain() {
    Color? color;
    if (colorHex != null) {
      color = Color(int.parse(colorHex!.replaceFirst('#', '0xFF')));
    }

    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      fontFamily: fontFamily,
      color: color,
      letterSpacing: letterSpacing,
      height: height,
    );
  }

  /// Create from TextStyle
  factory TextStyleModel.fromDomain(TextStyle textStyle) {
    return TextStyleModel(
      fontSize: textStyle.fontSize,
      fontWeight: textStyle.fontWeight,
      fontFamily: textStyle.fontFamily,
      colorHex: textStyle.color != null
          ? '#${textStyle.color!.toARGB32().toRadixString(16).padLeft(8, '0')}'
          : null,
      letterSpacing: textStyle.letterSpacing,
      height: textStyle.height,
    );
  }

  /// Helper method to convert FontWeight from JSON
  static FontWeight? _fontWeightFromJson(int? weightIndex) {
    if (weightIndex == null) return null;
    return FontWeight.values[weightIndex];
  }

  /// Helper method to convert FontWeight to JSON
  static int? _fontWeightToJson(FontWeight? fontWeight) {
    return fontWeight?.index;
  }

  @override
  List<Object?> get props => [
        fontSize,
        fontWeight,
        fontFamily,
        colorHex,
        letterSpacing,
        height,
      ];
}
