import 'package:flutter/material.dart';
import 'package:aplikasi_sppg/app/constants/app_colors.dart';

/// A widget that displays a skeleton loading animation
class SkeletonLoader extends StatefulWidget {
  /// The width of the skeleton
  final double? width;

  /// The height of the skeleton
  final double? height;

  /// The border radius of the skeleton
  final double borderRadius;

  /// Whether to show the shimmer effect
  final bool showShimmer;

  /// The shape of the skeleton
  final BoxShape shape;

  const SkeletonLoader({
    super.key,
    this.width,
    this.height,
    this.borderRadius = 8,
    this.showShimmer = true,
    this.shape = BoxShape.rectangle,
  });

  @override
  State<SkeletonLoader> createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    // Set up animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Create shimmer animation
    _animation = Tween<double>(begin: -2, end: 2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutSine),
    )..addListener(() {
      setState(() {});
    });

    // Start animation loop
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: AppColors.skeletonBase,
        borderRadius:
            widget.shape == BoxShape.circle
                ? null
                : BorderRadius.circular(widget.borderRadius),
        shape: widget.shape,
        gradient: widget.showShimmer ? _buildShimmerGradient() : null,
      ),
    );
  }

  /// Build shimmer gradient based on animation value
  LinearGradient _buildShimmerGradient() {
    return LinearGradient(
      begin: Alignment(_animation.value, 0),
      end: const Alignment(1, 0),
      colors: const [
        AppColors.skeletonBase,
        AppColors.skeletonHighlight,
        AppColors.skeletonBase,
      ],
      stops: const [0.1, 0.5, 0.9],
    );
  }
}

/// A widget that displays a skeleton card for KPI cards
class SimpleKPICardSkeleton extends StatelessWidget {
  /// The aspect ratio of the card
  final double aspectRatio;

  const SimpleKPICardSkeleton({super.key, this.aspectRatio = 1.2});

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: aspectRatio,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[20]!, width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon and title row
            Row(
              children: [
                const SkeletonLoader(
                  width: 32,
                  height: 32,
                  shape: BoxShape.circle,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      SkeletonLoader(width: 100, height: 14),
                      SizedBox(height: 4),
                      SkeletonLoader(width: 60, height: 10),
                    ],
                  ),
                ),
              ],
            ),
            const Spacer(),
            // Value
            const SkeletonLoader(width: 80, height: 24),
            const SizedBox(height: 8),
            // Trend indicator
            const SkeletonLoader(width: 100, height: 16),
          ],
        ),
      ),
    );
  }
}

/// A widget that displays a skeleton for action items
class SimpleActionItemSkeleton extends StatelessWidget {
  const SimpleActionItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[20]!, width: 1),
      ),
      child: Row(
        children: [
          // Left content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: const [
                SkeletonLoader(width: 120, height: 16),
                SizedBox(height: 8),
                SkeletonLoader(width: 200, height: 12),
                SizedBox(height: 8),
                SkeletonLoader(width: 80, height: 12),
              ],
            ),
          ),
          // Action buttons
          Column(
            children: const [
              SkeletonLoader(width: 80, height: 32, borderRadius: 4),
              SizedBox(height: 8),
              SkeletonLoader(width: 80, height: 32, borderRadius: 4),
            ],
          ),
        ],
      ),
    );
  }
}

/// A widget that displays a skeleton for the map component
class SimpleMapSkeleton extends StatelessWidget {
  /// The aspect ratio of the map
  final double aspectRatio;

  const SimpleMapSkeleton({super.key, this.aspectRatio = 16 / 9});

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: aspectRatio,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[20]!, width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: const [
                  SkeletonLoader(width: 150, height: 20),
                  Spacer(),
                  SkeletonLoader(width: 100, height: 32, borderRadius: 4),
                ],
              ),
            ),
            // Map content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SkeletonLoader(
                  width: double.infinity,
                  height: double.infinity,
                  borderRadius: 8,
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}

/// A widget that displays a skeleton for the chart component
class SimpleChartSkeleton extends StatelessWidget {
  /// The aspect ratio of the chart
  final double aspectRatio;

  const SimpleChartSkeleton({super.key, this.aspectRatio = 16 / 9});

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: aspectRatio,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[20]!, width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: const [
                  SkeletonLoader(width: 150, height: 20),
                  Spacer(),
                  SkeletonLoader(width: 100, height: 32, borderRadius: 4),
                ],
              ),
            ),
            // Chart content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Legend
                    Row(
                      children: const [
                        SkeletonLoader(
                          width: 12,
                          height: 12,
                          shape: BoxShape.circle,
                        ),
                        SizedBox(width: 4),
                        SkeletonLoader(width: 60, height: 12),
                        SizedBox(width: 16),
                        SkeletonLoader(
                          width: 12,
                          height: 12,
                          shape: BoxShape.circle,
                        ),
                        SizedBox(width: 4),
                        SkeletonLoader(width: 60, height: 12),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Chart bars
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          _buildChartBar(0.7),
                          _buildChartBar(0.4),
                          _buildChartBar(0.9),
                          _buildChartBar(0.5),
                          _buildChartBar(0.6),
                          _buildChartBar(0.3),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    // X-axis labels
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: const [
                        SkeletonLoader(width: 30, height: 10),
                        SkeletonLoader(width: 30, height: 10),
                        SkeletonLoader(width: 30, height: 10),
                        SkeletonLoader(width: 30, height: 10),
                        SkeletonLoader(width: 30, height: 10),
                        SkeletonLoader(width: 30, height: 10),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a chart bar with the given height ratio
  Widget _buildChartBar(double heightRatio) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SkeletonLoader(
              width: double.infinity,
              height: 100 * heightRatio,
              borderRadius: 4,
            ),
          ],
        ),
      ),
    );
  }
}

/// A widget that displays a skeleton for the activity feed
class SimpleActivityFeedSkeleton extends StatelessWidget {
  /// The number of activity items to show
  final int itemCount;

  const SimpleActivityFeedSkeleton({super.key, this.itemCount = 5});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[20]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: const [
                SkeletonLoader(width: 150, height: 20),
                Spacer(),
                SkeletonLoader(width: 32, height: 32, shape: BoxShape.circle),
              ],
            ),
          ),
          // Activity items
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: itemCount,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) => _buildActivityItem(),
          ),
        ],
      ),
    );
  }

  /// Build a single activity item skeleton
  Widget _buildActivityItem() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SkeletonLoader(width: 32, height: 32, shape: BoxShape.circle),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: const [
                SkeletonLoader(width: 120, height: 16),
                SizedBox(height: 8),
                SkeletonLoader(width: 200, height: 12),
                SizedBox(height: 8),
                SkeletonLoader(width: 80, height: 10),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
