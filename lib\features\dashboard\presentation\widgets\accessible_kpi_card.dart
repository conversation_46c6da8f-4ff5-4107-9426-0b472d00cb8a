import 'package:fluent_ui/fluent_ui.dart';
import '../../../../app/constants/app_spacing.dart';
import 'accessibility_utils.dart';
import 'high_contrast_mode.dart';
import 'semantic_label_helper.dart';

/// An accessible KPI card component with screen reader support and keyboard navigation
class AccessibleKpiCard extends StatefulWidget {
  /// Title of the KPI card
  final String title;

  /// Value to display
  final String value;

  /// Subtitle or description
  final String? subtitle;

  /// Icon to display
  final IconData icon;

  /// Background color
  final Color backgroundColor;

  /// Icon color
  final Color iconColor;

  /// Trend indicator (up, down, neutral)
  final String? trend;

  /// Trend value
  final String? trendValue;

  /// Whether the card is interactive
  final bool isInteractive;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Focus node for keyboard navigation
  final FocusNode? focusNode;

  const AccessibleKpiCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.subtitle,
    this.backgroundColor = Colors.blue,
    this.iconColor = Colors.white,
    this.trend,
    this.trendValue,
    this.isInteractive = false,
    this.onTap,
    this.focusNode,
  });

  @override
  State<AccessibleKpiCard> createState() => _AccessibleKpiCardState();
}

class _AccessibleKpiCardState extends State<AccessibleKpiCard> {
  late FocusNode _focusNode;
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Create semantic label for screen readers
    final semanticLabel = SemanticLabelHelper.createKpiLabel(
      title: widget.title,
      value: widget.value,
      trend: widget.trend,
      subtitle: widget.subtitle,
    );

    return HighContrastAwareWidget(
      normalBuilder: (context) => _buildNormalCard(context, semanticLabel),
      highContrastBuilder:
          (context) => _buildHighContrastCard(context, semanticLabel),
    );
  }

  Widget _buildNormalCard(BuildContext context, String semanticLabel) {
    return AccessibilityUtils.withKeyboardFocus(
      focusNode: _focusNode,
      onEnter: () => setState(() => _isHovered = true),
      onExit: () => setState(() => _isHovered = false),
      child: AccessibilityUtils.withSemanticLabel(
        label: semanticLabel,
        child: _buildCardContent(context),
      ),
    );
  }

  Widget _buildHighContrastCard(BuildContext context, String semanticLabel) {
    return AccessibilityUtils.withKeyboardFocus(
      focusNode: _focusNode,
      onEnter: () => setState(() => _isHovered = true),
      onExit: () => setState(() => _isHovered = false),
      child: AccessibilityUtils.withSemanticLabel(
        label: semanticLabel,
        child: Container(
          decoration: BoxDecoration(
            color: HighContrastColors.background,
            border: Border.all(
              color:
                  _isHovered || _focusNode.hasFocus
                      ? HighContrastColors.accent
                      : HighContrastColors.border,
              width: 2.0,
            ),
          ),
          child: _buildCardContent(context, isHighContrast: true),
        ),
      ),
    );
  }

  Widget _buildCardContent(
    BuildContext context, {
    bool isHighContrast = false,
  }) {
    final theme = FluentTheme.of(context);

    // Determine colors based on high contrast mode
    final bgColor =
        isHighContrast ? HighContrastColors.background : widget.backgroundColor;

    final iconColor =
        isHighContrast ? HighContrastColors.accent : widget.iconColor;

    final textColor =
        isHighContrast
            ? HighContrastColors.text
            : theme.brightness == Brightness.dark
            ? Colors.white
            : Colors.black;

    // Build the card content
    Widget content = Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and title row
          Row(
            children: [
              AccessibilityUtils.accessibleIcon(
                widget.icon,
                context: context,
                color: iconColor,
                size: 24.0,
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: AccessibilityUtils.accessibleText(
                  widget.title,
                  context: context,
                  style: theme.typography.bodyStrong.copyWith(color: textColor),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppSpacing.md),

          // Value
          AccessibilityUtils.accessibleText(
            widget.value,
            context: context,
            style: theme.typography.title?.copyWith(
              color: textColor,
              fontWeight: FontWeight.bold,
            ),
          ),

          // Subtitle if provided
          if (widget.subtitle != null) ...[
            const SizedBox(height: AppSpacing.xs),
            AccessibilityUtils.accessibleText(
              widget.subtitle!,
              context: context,
              style: theme.typography.caption.copyWith(
                color: textColor.withOpacity(0.7),
              ),
            ),
          ],

          // Trend if provided
          if (widget.trend != null && widget.trendValue != null) ...[
            const SizedBox(height: AppSpacing.sm),
            Row(
              children: [
                Icon(
                  widget.trend == 'up'
                      ? FluentIcons.chevron_up
                      : widget.trend == 'down'
                      ? FluentIcons.chevron_down
                      : FluentIcons.circle_fill,
                  size: 12.0,
                  color:
                      widget.trend == 'up'
                          ? isHighContrast
                              ? HighContrastColors.success
                              : Colors.green
                          : widget.trend == 'down'
                          ? isHighContrast
                              ? HighContrastColors.error
                              : Colors.red
                          : isHighContrast
                          ? HighContrastColors.info
                          : Colors.blue,
                ),
                const SizedBox(width: AppSpacing.xs),
                AccessibilityUtils.accessibleText(
                  widget.trendValue!,
                  context: context,
                  style: theme.typography.caption.copyWith(
                    color:
                        widget.trend == 'up'
                            ? isHighContrast
                                ? HighContrastColors.success
                                : Colors.green
                            : widget.trend == 'down'
                            ? isHighContrast
                                ? HighContrastColors.error
                                : Colors.red
                            : isHighContrast
                            ? HighContrastColors.info
                            : Colors.blue,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );

    // Make the card interactive if needed
    if (widget.isInteractive && widget.onTap != null) {
      content = GestureDetector(
        onTap: widget.onTap,
        onTapDown: (_) => setState(() => _isPressed = true),
        onTapUp: (_) => setState(() => _isPressed = false),
        onTapCancel: () => setState(() => _isPressed = false),
        child: content,
      );

      // Add keyboard activation
      content = KeyboardListener(
        focusNode: _focusNode,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.enter ||
                event.logicalKey == LogicalKeyboardKey.space) {
              widget.onTap?.call();
            }
          }
        },
        child: content,
      );
    }

    // Apply container styling
    return Container(
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow:
            isHighContrast
                ? null
                : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4.0,
                    offset: const Offset(0, 2),
                  ),
                ],
        border:
            isHighContrast
                ? null
                : _isHovered || _focusNode.hasFocus
                ? Border.all(color: theme.accentColor, width: 2.0)
                : null,
      ),
      child: content,
    );
  }
}
