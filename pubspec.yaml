name: aplikasi_sppg
description: "Sistem Operasional Dapur MBG (SOD-MBG) untuk mendukung operasional dapur SPPG dalam Program Makan Bergizi Grat<PERSON>."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  logger: ^2.6.0 # Add logger for debugging
  desktop_window: ^0.4.2
  powersync: ^1.0.0 # Add PowerSync for offline-first functionality
  go_router: ^7.0.0 # Add GoRouter for routing
  
  # Supabase dependencies for authentication
  supabase_flutter: ^2.5.6 # Supabase Flutter SDK
  shared_preferences: ^2.3.2 # For storing authentication state
  crypto: ^3.0.5 # For encryption utilities
  flutter_dotenv: ^5.1.0 # For loading environment variables
  
  # Additional utilities for authentication
  email_validator: ^2.1.17 # Email validation
  # flutter_secure_storage: ^9.2.2 # Secure storage for sensitive data - Temporarily disabled due to Windows build issues
  equatable: ^2.0.5 # For value comparison
  flutter_bloc: ^8.1.3
  bloc: ^8.1.2 # Core BLoC library
  meta: ^1.15.0 # Meta annotations
  
  # JSON serialization
  json_annotation: ^4.9.0 # JSON annotations for code generation
  
  # Fluent UI for Windows-style interface
  fluent_ui: ^4.12.0 # Fluent UI package for Windows-style interface
  
  # Connectivity
  connectivity_plus: ^6.0.5 # Network connectivity checking
  
  # Export functionality
  csv: ^6.0.0 # CSV export functionality
  excel: ^4.0.6 # Excel export functionality
  path_provider: ^2.1.4 # For file system access
  permission_handler: ^11.3.1 # For file permissions
  
  # HTTP client
  http: ^1.2.1 # HTTP requests

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0 # Ensure linting is included
  
  # Testing dependencies
  mockito: ^5.4.4 # Mock generation for testing
  build_runner: ^2.4.9 # Code generation runner
  json_serializable: ^6.8.0 # JSON serialization code generation
  provider: ^6.1.2 # Provider package for test widgets

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env
    - assets/dashboard_configs/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
