import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../../core/repositories/cached_repository.dart';
import '../../../../../core/utils/app_error.dart';
import '../../../../../core/utils/input_sanitizer.dart';
import '../../../../../core/utils/secure_error_handler.dart';
import '../../../../../core/services/audit_service.dart';
import '../../domain/models/sppg.dart';
import '../../domain/repositories/sppg_repository.dart';

/// Repository implementation for SPPG data using Supabase with caching
class SppgSupabaseRepository extends CachedRepository<Sppg>
    implements SppgRepository {
  static final Logger _logger = Logger();

  @override
  String get tableName => 'sppg';

  @override
  Sppg fromJson(Map<String, dynamic> json) {
    return Sppg.fromJson(json);
  }

  @override
  Map<String, dynamic> toCreateJson(Sppg model) {
    return model.toCreateRequest();
  }

  @override
  Map<String, dynamic> toUpdateJson(Sppg model) {
    return model.toUpdateRequest();
  }

  @override
  String getId(Sppg model) {
    return model.id;
  }

  /// Apply search functionality for SPPG
  @override
  dynamic applySearch(dynamic query, String searchQuery) {
    final searchTerm = searchQuery.toLowerCase().trim();

    // Search in nama, alamat, and email fields
    return query.or(
      'nama.ilike.%$searchTerm%,'
      'alamat.ilike.%$searchTerm%,'
      'email.ilike.%$searchTerm%',
    );
  }

  /// Get SPPG with enhanced filtering options
  Future<List<Sppg>> getSppgWithFilters({
    String? searchQuery,
    SppgStatus? status,
    SppgType? type,
    String? kepalaSppgId,
    String? perwakilanYayasanId,
    String? orderBy = 'nama',
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      _logger.d('Getting SPPG with filters');

      final filters = <String, dynamic>{};

      if (status != null) {
        filters['status'] = status.name;
      }

      if (type != null) {
        filters['type'] = type.name;
      }

      if (kepalaSppgId != null && kepalaSppgId.isNotEmpty) {
        filters['kepala_sppg_id'] = kepalaSppgId;
      }

      if (perwakilanYayasanId != null && perwakilanYayasanId.isNotEmpty) {
        filters['perwakilan_yayasan_id'] = perwakilanYayasanId;
      }

      return await getAll(
        searchQuery: searchQuery,
        filters: filters,
        orderBy: orderBy,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );
    } catch (e, stackTrace) {
      _logger.e('Failed to get SPPG with filters: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get SPPG by status
  @override
  Future<List<Sppg>> getSppgByStatus(SppgStatus status) async {
    try {
      _logger.d('Getting SPPG by status: ${status.name}');

      return await getAll(
        filters: {'status': status.name},
        orderBy: 'nama',
        ascending: true,
      );
    } catch (e, stackTrace) {
      _logger.e('Failed to get SPPG by status: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get SPPG by type
  @override
  Future<List<Sppg>> getSppgByType(SppgType type) async {
    try {
      _logger.d('Getting SPPG by type: ${type.name}');

      return await getAll(
        filters: {'type': type.name},
        orderBy: 'nama',
        ascending: true,
      );
    } catch (e, stackTrace) {
      _logger.e('Failed to get SPPG by type: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get SPPG assigned to a specific Kepala SPPG
  Future<List<Sppg>> getSppgByKepalaSppg(String kepalaSppgId) async {
    try {
      _logger.d('Getting SPPG by Kepala SPPG: $kepalaSppgId');

      return await getAll(
        filters: {'kepala_sppg_id': kepalaSppgId},
        orderBy: 'nama',
        ascending: true,
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG by Kepala SPPG: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get SPPG assigned to a specific Perwakilan Yayasan
  Future<List<Sppg>> getSppgByPerwakilanYayasan(
    String perwakilanYayasanId,
  ) async {
    try {
      _logger.d('Getting SPPG by Perwakilan Yayasan: $perwakilanYayasanId');

      return await getAll(
        filters: {'perwakilan_yayasan_id': perwakilanYayasanId},
        orderBy: 'nama',
        ascending: true,
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG by Perwakilan Yayasan: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update SPPG status
  Future<Sppg> updateStatus(String id, SppgStatus newStatus) async {
    try {
      _logger.d('Updating SPPG status: $id to ${newStatus.name}');

      final response =
          await client
              .from(tableName)
              .update({
                'status': newStatus.name,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', id)
              .select()
              .single();

      _logger.i('SPPG status updated successfully: $id');
      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e('Failed to update SPPG status: $e', stackTrace: stackTrace);
      throw _handleError(e, 'update status');
    }
  }

  /// Assign Kepala SPPG (internal method)
  Future<Sppg> _assignKepalaSppgInternal(
    String sppgId,
    String kepalaSppgId,
    String kepalaSppgNama,
  ) async {
    try {
      _logger.d('Assigning Kepala SPPG: $kepalaSppgId to SPPG: $sppgId');

      final response =
          await client
              .from(tableName)
              .update({
                'kepala_sppg_id': kepalaSppgId,
                'kepala_sppg_nama': kepalaSppgNama,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', sppgId)
              .select()
              .single();

      _logger.i('Kepala SPPG assigned successfully');
      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e('Failed to assign Kepala SPPG: $e', stackTrace: stackTrace);
      throw _handleError(e, 'assign kepala sppg');
    }
  }

  /// Assign Perwakilan Yayasan (internal method)
  Future<Sppg> _assignPerwakilanYayasanInternal(
    String sppgId,
    String perwakilanYayasanId,
    String perwakilanYayasanNama,
  ) async {
    try {
      _logger.d(
        'Assigning Perwakilan Yayasan: $perwakilanYayasanId to SPPG: $sppgId',
      );

      final response =
          await client
              .from(tableName)
              .update({
                'perwakilan_yayasan_id': perwakilanYayasanId,
                'perwakilan_yayasan_nama': perwakilanYayasanNama,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', sppgId)
              .select()
              .single();

      _logger.i('Perwakilan Yayasan assigned successfully');
      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to assign Perwakilan Yayasan: $e',
        stackTrace: stackTrace,
      );
      throw _handleError(e, 'assign perwakilan yayasan');
    }
  }

  /// Remove assignment (set to null)
  Future<Sppg> removeAssignment(
    String sppgId, {
    bool removeKepala = false,
    bool removePerwakilan = false,
  }) async {
    try {
      _logger.d('Removing assignment from SPPG: $sppgId');

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (removeKepala) {
        updateData['kepala_sppg_id'] = null;
        updateData['kepala_sppg_nama'] = null;
      }

      if (removePerwakilan) {
        updateData['perwakilan_yayasan_id'] = null;
        updateData['perwakilan_yayasan_nama'] = null;
      }

      final response =
          await client
              .from(tableName)
              .update(updateData)
              .eq('id', sppgId)
              .select()
              .single();

      _logger.i('Assignment removed successfully');
      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e('Failed to remove assignment: $e', stackTrace: stackTrace);
      throw _handleError(e, 'remove assignment');
    }
  }

  /// Get SPPG statistics
  Future<Map<String, int>> getSppgStatistics() async {
    try {
      _logger.d('Getting SPPG statistics');

      // Get all SPPG data for statistics calculation
      final allSppg = await getAll();

      final stats = <String, int>{
        'total': allSppg.length,
        'aktif': allSppg.where((s) => s.status == SppgStatus.aktif).length,
        'nonAktif':
            allSppg.where((s) => s.status == SppgStatus.nonAktif).length,
        'suspend': allSppg.where((s) => s.status == SppgStatus.suspend).length,
        'milikYayasan':
            allSppg.where((s) => s.type == SppgType.milikYayasan).length,
        'mitra': allSppg.where((s) => s.type == SppgType.mitra).length,
        'totalKapasitas': allSppg.fold<int>(
          0,
          (sum, s) => sum + s.kapasitasHarian,
        ),
        'kapasitasAktif': allSppg
            .where((s) => s.status == SppgStatus.aktif)
            .fold<int>(0, (sum, s) => sum + s.kapasitasHarian),
      };

      _logger.i('SPPG statistics calculated: $stats');
      return stats;
    } catch (e, stackTrace) {
      _logger.e('Failed to get SPPG statistics: $e', stackTrace: stackTrace);
      throw _handleError(e, 'get statistics');
    }
  }

  /// Check if SPPG can be safely deleted
  Future<bool> canDelete(String id) async {
    try {
      _logger.d('Checking if SPPG can be deleted: $id');

      // Check if SPPG has assigned users
      final userCountResponse =
          await client
              .from('user_profiles')
              .select('id')
              .eq('sppg_id', id)
              .count();

      final userCount = userCountResponse.count;

      // Check if SPPG has active operations (this would depend on your business logic)
      // For now, we'll just check the status
      final sppg = await getById(id);
      if (sppg == null) return false;

      final canDelete = userCount == 0 && sppg.status != SppgStatus.aktif;

      _logger.d(
        'SPPG can be deleted: $canDelete (users: $userCount, status: ${sppg.status})',
      );
      return canDelete;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to check if SPPG can be deleted: $e',
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Delete SPPG with safety checks
  @override
  Future<void> delete(String id) async {
    try {
      _logger.d('Attempting to delete SPPG: $id');

      // Check if deletion is safe
      final canDeleteSafely = await canDelete(id);
      if (!canDeleteSafely) {
        throw AppError(
          type: ErrorType.validation,
          message:
              'SPPG tidak dapat dihapus karena masih memiliki pengguna yang ditugaskan atau masih aktif',
          operation: 'delete',
          timestamp: DateTime.now(),
        );
      }

      await super.delete(id);
    } catch (e, stackTrace) {
      _logger.e('Failed to delete SPPG: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Subscribe to SPPG changes with filtering
  RealtimeChannel subscribeToSppgChanges({
    required void Function(Sppg) onInsert,
    required void Function(Sppg) onUpdate,
    required void Function(String) onDelete,
    SppgStatus? statusFilter,
    SppgType? typeFilter,
  }) {
    _logger.d('Subscribing to SPPG changes');

    // Instead of creating a filter, we'll handle filtering in the callbacks
    // after receiving the data

    // Create a custom channel for this specific subscription
    final channel = client.channel('$tableName-filtered-changes');

    // Add insert event subscription
    channel.onPostgresChanges(
      event: PostgresChangeEvent.insert,
      schema: 'public',
      table: tableName,
      callback: (payload) {
        try {
          final record = fromJson(payload.newRecord);

          // Apply filters before notifying
          bool matchesFilter = true;

          if (statusFilter != null && record.status != statusFilter) {
            matchesFilter = false;
          }

          if (typeFilter != null && record.type != typeFilter) {
            matchesFilter = false;
          }

          if (matchesFilter) {
            onInsert(record);
          }
        } catch (e) {
          _logger.e('Error processing insert event: $e');
        }
      },
    );

    // Add update event subscription
    channel.onPostgresChanges(
      event: PostgresChangeEvent.update,
      schema: 'public',
      table: tableName,
      callback: (payload) {
        try {
          final record = fromJson(payload.newRecord);

          // Apply filters before notifying
          bool matchesFilter = true;

          if (statusFilter != null && record.status != statusFilter) {
            matchesFilter = false;
          }

          if (typeFilter != null && record.type != typeFilter) {
            matchesFilter = false;
          }

          if (matchesFilter) {
            onUpdate(record);
          }
        } catch (e) {
          _logger.e('Error processing update event: $e');
        }
      },
    );

    // Add delete event subscription
    channel.onPostgresChanges(
      event: PostgresChangeEvent.delete,
      schema: 'public',
      table: tableName,
      callback: (payload) {
        try {
          final id = payload.oldRecord['id'] as String;

          // For delete events, we need to check the old record
          bool matchesFilter = true;

          if (statusFilter != null) {
            final status = SppgStatus.values.firstWhere(
              (s) => s.name == payload.oldRecord['status'],
              orElse: () => SppgStatus.nonAktif, // Default
            );
            if (status != statusFilter) {
              matchesFilter = false;
            }
          }

          if (typeFilter != null) {
            final type = SppgType.values.firstWhere(
              (t) => t.name == payload.oldRecord['type'],
              orElse: () => SppgType.mitra, // Default
            );
            if (type != typeFilter) {
              matchesFilter = false;
            }
          }

          if (matchesFilter) {
            onDelete(id);
          }
        } catch (e) {
          _logger.e('Error processing delete event: $e');
        }
      },
    );

    channel.subscribe();
    return channel;
  }

  /// Sanitize SPPG input data
  Sppg _sanitizeSppgInput(Sppg model) {
    return model.copyWith(
      nama: InputSanitizer.sanitizeText(model.nama),
      alamat: InputSanitizer.sanitizeText(model.alamat),
      email:
          model.email != null
              ? InputSanitizer.sanitizeEmail(model.email)
              : null,
      noTelepon:
          model.noTelepon != null
              ? InputSanitizer.sanitizePhoneNumber(model.noTelepon)
              : null,
      kepalaSppgNama:
          model.kepalaSppgNama != null
              ? InputSanitizer.sanitizeText(model.kepalaSppgNama)
              : null,
      perwakilanYayasanNama:
          model.perwakilanYayasanNama != null
              ? InputSanitizer.sanitizeText(model.perwakilanYayasanNama)
              : null,
    );
  }

  /// Create with audit logging
  @override
  Future<Sppg> create(Sppg model) async {
    try {
      // Sanitize input data
      final sanitizedModel = _sanitizeSppgInput(model);

      final result = await super.create(sanitizedModel);

      // Audit log the creation
      await AuditService.instance.logSppgManagement(
        performedBy: client.auth.currentUser?.id ?? 'system',
        action: AuditActions.createSppg,
        sppgId: result.id,
        result: AuditResults.success,
        metadata: {'sppg_name': result.nama, 'sppg_type': result.type.name},
      );

      return result;
    } catch (e) {
      // Audit log the failure
      await AuditService.instance.logSppgManagement(
        performedBy: client.auth.currentUser?.id ?? 'system',
        action: AuditActions.createSppg,
        sppgId: model.id,
        result: AuditResults.failure,
        metadata: {'error': e.toString(), 'sppg_name': model.nama},
      );

      throw SecureErrorHandler.handleError(
        e,
        'create SPPG',
        userId: client.auth.currentUser?.id,
        context: 'SPPG creation',
      );
    }
  }

  /// Update with audit logging
  @override
  Future<Sppg> update(Sppg model) async {
    try {
      // Get original data for audit trail
      final original = await getById(getId(model));

      // Sanitize input data
      final sanitizedModel = _sanitizeSppgInput(model);

      final result = await super.update(sanitizedModel);

      // Audit log the update
      await AuditService.instance.logSppgManagement(
        performedBy: client.auth.currentUser?.id ?? 'system',
        action: AuditActions.updateSppg,
        sppgId: result.id,
        result: AuditResults.success,
        changes: {'old': original?.toJson(), 'new': result.toJson()},
        metadata: {'sppg_name': result.nama},
      );

      return result;
    } catch (e) {
      // Audit log the failure
      await AuditService.instance.logSppgManagement(
        performedBy: client.auth.currentUser?.id ?? 'system',
        action: AuditActions.updateSppg,
        sppgId: getId(model),
        result: AuditResults.failure,
        metadata: {'error': e.toString(), 'sppg_name': model.nama},
      );

      throw SecureErrorHandler.handleError(
        e,
        'update SPPG',
        userId: client.auth.currentUser?.id,
        context: 'SPPG update',
      );
    }
  }

  /// Delete with audit logging
  Future<void> safeDelete(String id) async {
    try {
      // Get original data for audit trail
      final original = await getById(id);

      await super.delete(id);

      // Audit log the deletion
      await AuditService.instance.logSppgManagement(
        performedBy: client.auth.currentUser?.id ?? 'system',
        action: AuditActions.deleteSppg,
        sppgId: id,
        result: AuditResults.success,
        changes: {'deleted': original?.toJson()},
        metadata: {'sppg_name': original?.nama},
      );
    } catch (e) {
      // Audit log the failure
      await AuditService.instance.logSppgManagement(
        performedBy: client.auth.currentUser?.id ?? 'system',
        action: AuditActions.deleteSppg,
        sppgId: id,
        result: AuditResults.failure,
        metadata: {'error': e.toString()},
      );

      throw SecureErrorHandler.handleError(
        e,
        'delete SPPG',
        userId: client.auth.currentUser?.id,
        context: 'SPPG deletion',
      );
    }
  }

  /// Handle and convert errors to AppError
  AppError _handleError(dynamic error, String operation) {
    return SecureErrorHandler.handleError(
      error,
      operation,
      userId: client.auth.currentUser?.id,
      context: 'SPPG repository',
    );
  }

  // ===== IMPLEMENTING MISSING SPPG REPOSITORY METHODS =====

  @override
  Future<List<Sppg>> getAllSppg() async {
    return await getAll();
  }

  @override
  Future<Sppg?> getSppgById(String id) async {
    return await getById(id);
  }

  @override
  Future<Sppg> createSppg(Sppg sppg) async {
    return await create(sppg);
  }

  @override
  Future<Sppg> updateSppg(Sppg sppg) async {
    return await update(sppg);
  }

  @override
  Future<bool> deleteSppg(String id) async {
    try {
      await delete(id);
      return true;
    } catch (e) {
      _logger.e('Failed to delete SPPG: $e');
      return false;
    }
  }

  @override
  Future<List<Sppg>> searchSppgByName(String query) async {
    return await getAll(searchQuery: query);
  }

  @override
  Future<List<Sppg>> getSppgByStatusAndType(
    SppgStatus status,
    SppgType type,
  ) async {
    return await getAll(filters: {'status': status.name, 'type': type.name});
  }

  @override
  Future<List<Sppg>> getSppgByYayasan(String yayasanId) async {
    return await getAll(filters: {'yayasan_id': yayasanId});
  }

  @override
  Future<bool> assignKepalaSppg(
    String sppgId,
    String userId,
    String nama,
  ) async {
    try {
      await _assignKepalaSppgInternal(sppgId, userId, nama);
      return true;
    } catch (e) {
      _logger.e('Failed to assign kepala SPPG: $e');
      return false;
    }
  }

  @override
  Future<bool> removeKepalaSppg(String sppgId) async {
    try {
      await removeAssignment(sppgId, removeKepala: true);
      return true;
    } catch (e) {
      _logger.e('Failed to remove kepala SPPG: $e');
      return false;
    }
  }

  @override
  Future<bool> assignPerwakilanYayasan(
    String sppgId,
    String userId,
    String nama,
  ) async {
    try {
      await _assignPerwakilanYayasanInternal(sppgId, userId, nama);
      return true;
    } catch (e) {
      _logger.e('Failed to assign perwakilan yayasan: $e');
      return false;
    }
  }

  @override
  Future<bool> removePerwakilanYayasan(String sppgId) async {
    try {
      await removeAssignment(sppgId, removePerwakilan: true);
      return true;
    } catch (e) {
      _logger.e('Failed to remove perwakilan yayasan: $e');
      return false;
    }
  }

  @override
  Future<bool> updateSppgStatus(String id, SppgStatus status) async {
    try {
      await updateStatus(id, status);
      return true;
    } catch (e) {
      _logger.e('Failed to update SPPG status: $e');
      return false;
    }
  }

  @override
  Future<bool> activateSppg(String id) async {
    return await updateSppgStatus(id, SppgStatus.aktif);
  }

  @override
  Future<bool> deactivateSppg(String id) async {
    return await updateSppgStatus(id, SppgStatus.nonAktif);
  }

  @override
  Future<bool> suspendSppg(String id) async {
    return await updateSppgStatus(id, SppgStatus.suspend);
  }

  @override
  Future<int> getTotalSppgCount() async {
    final allSppg = await getAll();
    return allSppg.length;
  }

  @override
  Future<Map<SppgStatus, int>> getSppgCountByStatus() async {
    final allSppg = await getAll();
    return {
      SppgStatus.aktif:
          allSppg.where((s) => s.status == SppgStatus.aktif).length,
      SppgStatus.nonAktif:
          allSppg.where((s) => s.status == SppgStatus.nonAktif).length,
      SppgStatus.suspend:
          allSppg.where((s) => s.status == SppgStatus.suspend).length,
    };
  }

  @override
  Future<Map<SppgType, int>> getSppgCountByType() async {
    final allSppg = await getAll();
    return {
      SppgType.milikYayasan:
          allSppg.where((s) => s.type == SppgType.milikYayasan).length,
      SppgType.mitra: allSppg.where((s) => s.type == SppgType.mitra).length,
    };
  }

  @override
  Future<List<Sppg>> getSppgWithoutKepala() async {
    return await getAll(filters: {'kepala_sppg_id': null});
  }

  @override
  Future<List<Sppg>> getMitraSppgWithoutPerwakilan() async {
    return await getAll(
      filters: {'type': SppgType.mitra.name, 'perwakilan_yayasan_id': null},
    );
  }

  @override
  Future<bool> isSppgNameExists(String nama, {String? excludeId}) async {
    try {
      var query = client.from(tableName).select('id').eq('nama', nama);

      if (excludeId != null) {
        query = query.neq('id', excludeId);
      }

      final result = await query;
      return result.isNotEmpty;
    } catch (e) {
      _logger.e('Failed to check SPPG name existence: $e');
      return false;
    }
  }

  @override
  Future<bool> isSppgEmailExists(String email, {String? excludeId}) async {
    try {
      var query = client.from(tableName).select('id').eq('email', email);

      if (excludeId != null) {
        query = query.neq('id', excludeId);
      }

      final result = await query;
      return result.isNotEmpty;
    } catch (e) {
      _logger.e('Failed to check SPPG email existence: $e');
      return false;
    }
  }

  @override
  Future<List<Map<String, String>>> getAvailableKepalaSppg() async {
    try {
      final result = await client
          .from('user_profiles')
          .select('id, nama')
          .eq('role', 'kepala_dapur')
          .eq('status', 'active')
          .isFilter('sppg_id', null);

      return result
          .map<Map<String, String>>(
            (user) => {
              'id': user['id'] as String,
              'nama': user['nama'] as String,
            },
          )
          .toList();
    } catch (e) {
      _logger.e('Failed to get available kepala SPPG: $e');
      return [];
    }
  }

  @override
  Future<List<Map<String, String>>> getAvailablePerwakilanYayasan() async {
    try {
      final result = await client
          .from('user_profiles')
          .select('id, nama')
          .eq('role', 'perwakilan_yayasan')
          .eq('status', 'active');

      return result
          .map<Map<String, String>>(
            (user) => {
              'id': user['id'] as String,
              'nama': user['nama'] as String,
            },
          )
          .toList();
    } catch (e) {
      _logger.e('Failed to get available perwakilan yayasan: $e');
      return [];
    }
  }

  @override
  Future<bool> bulkUpdateStatus(List<String> sppgIds, SppgStatus status) async {
    try {
      await client
          .from(tableName)
          .update({
            'status': status.name,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .inFilter('id', sppgIds);

      return true;
    } catch (e) {
      _logger.e('Failed to bulk update SPPG status: $e');
      return false;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> exportSppgData({
    SppgStatus? status,
    SppgType? type,
  }) async {
    try {
      final filters = <String, dynamic>{};

      if (status != null) {
        filters['status'] = status.name;
      }

      if (type != null) {
        filters['type'] = type.name;
      }

      final sppgs = await getAll(filters: filters);
      return sppgs.map((sppg) => sppg.toJson()).toList();
    } catch (e) {
      _logger.e('Failed to export SPPG data: $e');
      return [];
    }
  }

  @override
  Future<void> clearCache() async {
    // Implementation depends on your caching strategy
    // For now, we'll just log it
    _logger.d('Cache cleared for SPPG repository');
  }

  @override
  Future<void> refreshData() async {
    await clearCache();
    // Force refresh by clearing any cached data
    _logger.d('Data refreshed for SPPG repository');
  }
}
