import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/app/constants/app_theme_colors.dart';

void main() {
  group('AppThemeColors', () {
    group('Color Constants', () {
      test('should have correct dark theme colors', () {
        expect(AppThemeColors.darkBackground.value, equals(0xFF1E1E2F));
        expect(AppThemeColors.darkPanel.value, equals(0xFF2A2A40));
        expect(AppThemeColors.darkTextPrimary.value, equals(0xFFFFFFFF));
        expect(AppThemeColors.darkTextSecondary.value, equals(0xFFA0A3BD));
        expect(AppThemeColors.darkDivider.value, equals(0xFF3E4059));
      });

      test('should have correct light theme colors', () {
        expect(AppThemeColors.lightBackground.value, equals(0xFFF8F9FB));
        expect(AppThemeColors.lightPanel.value, equals(0xFFFFFFFF));
        expect(AppThemeColors.lightTextPrimary.value, equals(0xFF1A1C1F));
        expect(AppThemeColors.lightTextSecondary.value, equals(0xFF5A5F73));
        expect(AppThemeColors.lightDivider.value, equals(0xFFE0E3EC));
      });

      test('should have consistent accent colors', () {
        expect(AppThemeColors.accentPrimary.value, equals(0xFF91C8E4));
        expect(AppThemeColors.accentSecondary.value, equals(0xFFFFFBDE));
      });

      test('should have correct status colors for dark theme', () {
        expect(AppThemeColors.statusDangerDark.value, equals(0xFFFF6B6B));
        expect(AppThemeColors.statusSafeDark.value, equals(0xFF3DD598));
        expect(AppThemeColors.statusWarningDark.value, equals(0xFFF4D35E));
      });

      test('should have correct status colors for light theme', () {
        expect(AppThemeColors.statusDangerLight.value, equals(0xFFD9534F));
        expect(AppThemeColors.statusSafeLight.value, equals(0xFF28A745));
        expect(AppThemeColors.statusWarningLight.value, equals(0xFFFFC107));
      });
    });

    group('Theme-Aware Color Getters', () {
      test('should return correct colors based on brightness', () {
        // Test light theme
        expect(
          AppThemeColors.getBackgroundColor(Brightness.light),
          equals(AppThemeColors.lightBackground),
        );
        expect(
          AppThemeColors.getPanelColor(Brightness.light),
          equals(AppThemeColors.lightPanel),
        );
        expect(
          AppThemeColors.getTextPrimaryColor(Brightness.light),
          equals(AppThemeColors.lightTextPrimary),
        );

        // Test dark theme
        expect(
          AppThemeColors.getBackgroundColor(Brightness.dark),
          equals(AppThemeColors.darkBackground),
        );
        expect(
          AppThemeColors.getPanelColor(Brightness.dark),
          equals(AppThemeColors.darkPanel),
        );
        expect(
          AppThemeColors.getTextPrimaryColor(Brightness.dark),
          equals(AppThemeColors.darkTextPrimary),
        );
      });

      test('should return correct status colors based on brightness', () {
        // Test light theme status colors
        expect(
          AppThemeColors.getStatusDangerColor(Brightness.light),
          equals(AppThemeColors.statusDangerLight),
        );
        expect(
          AppThemeColors.getStatusSafeColor(Brightness.light),
          equals(AppThemeColors.statusSafeLight),
        );
        expect(
          AppThemeColors.getStatusWarningColor(Brightness.light),
          equals(AppThemeColors.statusWarningLight),
        );

        // Test dark theme status colors
        expect(
          AppThemeColors.getStatusDangerColor(Brightness.dark),
          equals(AppThemeColors.statusDangerDark),
        );
        expect(
          AppThemeColors.getStatusSafeColor(Brightness.dark),
          equals(AppThemeColors.statusSafeDark),
        );
        expect(
          AppThemeColors.getStatusWarningColor(Brightness.dark),
          equals(AppThemeColors.statusWarningDark),
        );
      });
    });

    group('Semantic Status Colors', () {
      test('should handle danger aliases correctly', () {
        const dangerAliases = ['danger', 'error', 'critical', 'failed'];
        final expectedLight = AppThemeColors.statusDangerLight;
        final expectedDark = AppThemeColors.statusDangerDark;

        for (final alias in dangerAliases) {
          expect(
            AppThemeColors.getSemanticStatusColor(alias, Brightness.light),
            equals(expectedLight),
            reason: 'Alias "$alias" should return danger color for light theme',
          );
          expect(
            AppThemeColors.getSemanticStatusColor(alias, Brightness.dark),
            equals(expectedDark),
            reason: 'Alias "$alias" should return danger color for dark theme',
          );
        }
      });

      test('should handle safe aliases correctly', () {
        const safeAliases = ['safe', 'success', 'completed', 'passed'];
        final expectedLight = AppThemeColors.statusSafeLight;
        final expectedDark = AppThemeColors.statusSafeDark;

        for (final alias in safeAliases) {
          expect(
            AppThemeColors.getSemanticStatusColor(alias, Brightness.light),
            equals(expectedLight),
            reason: 'Alias "$alias" should return safe color for light theme',
          );
          expect(
            AppThemeColors.getSemanticStatusColor(alias, Brightness.dark),
            equals(expectedDark),
            reason: 'Alias "$alias" should return safe color for dark theme',
          );
        }
      });

      test('should handle warning aliases correctly', () {
        const warningAliases = ['warning', 'attention', 'pending', 'caution'];
        final expectedLight = AppThemeColors.statusWarningLight;
        final expectedDark = AppThemeColors.statusWarningDark;

        for (final alias in warningAliases) {
          expect(
            AppThemeColors.getSemanticStatusColor(alias, Brightness.light),
            equals(expectedLight),
            reason: 'Alias "$alias" should return warning color for light theme',
          );
          expect(
            AppThemeColors.getSemanticStatusColor(alias, Brightness.dark),
            equals(expectedDark),
            reason: 'Alias "$alias" should return warning color for dark theme',
          );
        }
      });

      test('should handle unknown status gracefully', () {
        // Unknown statuses should fall back to primary accent
        expect(
          AppThemeColors.getSemanticStatusColor('unknown', Brightness.light),
          equals(AppThemeColors.accentPrimary),
        );
        expect(
          AppThemeColors.getSemanticStatusColor('random', Brightness.dark),
          equals(AppThemeColors.accentPrimary),
        );
      });
    });

    group('Contrast and Accessibility', () {
      test('should calculate contrast ratios correctly', () {
        final white = Color(0xFFFFFFFF);
        final black = Color(0xFF000000);

        // Maximum contrast (black on white)
        final maxContrast = AppThemeColors.calculateContrastRatio(black, white);
        expect(maxContrast, closeTo(21.0, 0.1));

        // Minimum contrast (same color)
        final minContrast = AppThemeColors.calculateContrastRatio(white, white);
        expect(minContrast, closeTo(1.0, 0.1));
      });

      test('should validate WCAG AA compliance correctly', () {
        final white = Color(0xFFFFFFFF);
        final black = Color(0xFF000000);
        final mediumGray = Color(0xFF767676);
        final lightGray = Color(0xFFAAAAAA);

        // High contrast should pass
        expect(AppThemeColors.meetsWCAGAA(black, white), isTrue);
        expect(AppThemeColors.meetsWCAGAA(white, black), isTrue);

        // Medium contrast should pass for normal text
        expect(AppThemeColors.meetsWCAGAA(mediumGray, white), isTrue);

        // Low contrast should fail
        expect(AppThemeColors.meetsWCAGAA(lightGray, white), isFalse);

        // Large text has lower requirements
        expect(
          AppThemeColors.meetsWCAGAA(lightGray, white, isLargeText: true),
          isTrue,
        );
      });

      test('should provide accessible text colors', () {
        final lightBg = AppThemeColors.lightBackground;
        final darkBg = AppThemeColors.darkBackground;

        final accessibleLightText = AppThemeColors.getAccessibleTextColor(
          lightBg,
          Brightness.light,
        );
        final accessibleDarkText = AppThemeColors.getAccessibleTextColor(
          darkBg,
          Brightness.dark,
        );

        // Verify accessibility compliance
        expect(AppThemeColors.meetsWCAGAA(accessibleLightText, lightBg), isTrue);
        expect(AppThemeColors.meetsWCAGAA(accessibleDarkText, darkBg), isTrue);
      });

      test('should ensure theme colors meet accessibility standards', () {
        // Test light theme accessibility
        expect(
          AppThemeColors.meetsWCAGAA(
            AppThemeColors.lightTextPrimary,
            AppThemeColors.lightBackground,
          ),
          isTrue,
          reason: 'Light theme primary text should be accessible',
        );

        // Test dark theme accessibility
        expect(
          AppThemeColors.meetsWCAGAA(
            AppThemeColors.darkTextPrimary,
            AppThemeColors.darkBackground,
          ),
          isTrue,
          reason: 'Dark theme primary text should be accessible',
        );
      });
    });

    group('Interactive State Colors', () {
      test('should generate correct interactive state colors', () {
        final baseColor = AppThemeColors.accentPrimary;

        final hoverColor = AppThemeColors.getHoverColor(baseColor);
        final pressedColor = AppThemeColors.getPressedColor(baseColor);
        final disabledColor = AppThemeColors.getDisabledColor(baseColor);
        final focusColor = AppThemeColors.getFocusColor(baseColor);

        // All should be variations of the base color
        expect((hoverColor.r * 255.0).round() & 0xff, equals((baseColor.r * 255.0).round() & 0xff));
        expect((pressedColor.g * 255.0).round() & 0xff, equals((baseColor.g * 255.0).round() & 0xff));
        expect((disabledColor.b * 255.0).round() & 0xff, equals((baseColor.b * 255.0).round() & 0xff));
        expect((focusColor.r * 255.0).round() & 0xff, equals((baseColor.r * 255.0).round() & 0xff));

        // But with different alpha values
        expect((hoverColor.a * 255.0).round() & 0xff, lessThan((baseColor.a * 255.0).round() & 0xff));
        expect((pressedColor.a * 255.0).round() & 0xff, lessThan((hoverColor.a * 255.0).round() & 0xff));
        expect((disabledColor.a * 255.0).round() & 0xff, lessThan((baseColor.a * 255.0).round() & 0xff));
        expect((focusColor.a * 255.0).round() & 0xff, lessThan((baseColor.a * 255.0).round() & 0xff));
      });

      test('should handle opacity variations correctly', () {
        final baseColor = Color(0xFF91C8E4);
        
        final withOpacity50 = AppThemeColors.withOpacity(baseColor, 0.5);
        final withOpacity25 = AppThemeColors.withOpacity(baseColor, 0.25);

        expect((withOpacity50.a * 255.0).round() & 0xff, equals((255 * 0.5).round()));
        expect((withOpacity25.a * 255.0).round() & 0xff, equals((255 * 0.25).round()));
      });
    });

    group('Gradients', () {
      test('should provide theme-appropriate gradients', () {
        final primaryGradient = AppThemeColors.primaryGradient;
        expect(primaryGradient.colors, contains(AppThemeColors.accentPrimary));
        expect(primaryGradient.colors, contains(AppThemeColors.accentSecondary));

        final lightGradient = AppThemeColors.getBackgroundGradient(Brightness.light);
        expect(lightGradient.colors, contains(AppThemeColors.lightBackground));

        final darkGradient = AppThemeColors.getBackgroundGradient(Brightness.dark);
        expect(darkGradient.colors, contains(AppThemeColors.darkBackground));
      });
    });

    group('Color Palettes', () {
      test('should provide complete theme palettes', () {
        final lightPalette = AppThemeColors.lightThemePalette;
        final darkPalette = AppThemeColors.darkThemePalette;

        // Check all required keys are present
        const requiredKeys = [
          'background',
          'panel',
          'textPrimary',
          'textSecondary',
          'divider',
          'accentPrimary',
          'accentSecondary',
          'statusDanger',
          'statusSafe',
          'statusWarning',
        ];

        for (final key in requiredKeys) {
          expect(lightPalette.containsKey(key), isTrue, reason: 'Light palette missing $key');
          expect(darkPalette.containsKey(key), isTrue, reason: 'Dark palette missing $key');
        }
      });

      test('should return correct palette for brightness', () {
        final lightPalette = AppThemeColors.getThemePalette(Brightness.light);
        final darkPalette = AppThemeColors.getThemePalette(Brightness.dark);

        expect(lightPalette['background'], equals(AppThemeColors.lightBackground));
        expect(darkPalette['background'], equals(AppThemeColors.darkBackground));
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle null and edge cases gracefully', () {
        // Test with empty status string
        expect(
          AppThemeColors.getSemanticStatusColor('', Brightness.light),
          equals(AppThemeColors.accentPrimary),
        );

        // Test with null-like values
        expect(
          AppThemeColors.getSemanticStatusColor('null', Brightness.light),
          equals(AppThemeColors.accentPrimary),
        );
      });

      test('should handle extreme opacity values', () {
        final baseColor = Color(0xFF91C8E4);

        // Test extreme values
        final zeroOpacity = AppThemeColors.withOpacity(baseColor, 0.0);
        final fullOpacity = AppThemeColors.withOpacity(baseColor, 1.0);

        expect((zeroOpacity.a * 255.0).round() & 0xff, equals(0));
        expect((fullOpacity.a * 255.0).round() & 0xff, equals(255));
      });
    });
  });
}
