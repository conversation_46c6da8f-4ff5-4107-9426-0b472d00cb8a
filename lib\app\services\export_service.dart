import 'dart:convert';
import 'dart:io';
import 'package:csv/csv.dart';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';
import '../../features/admin/user_management/domain/models/user_management.dart';
import '../../features/admin/sppg_management/domain/models/sppg.dart';

/// Service untuk menangani export data ke berbagai format
abstract class ExportService {
  /// Export data ke format CSV
  Future<ExportResult> exportToCSV({
    required List<Map<String, dynamic>> data,
    required String filename,
    List<String>? selectedColumns,
    Function(double)? onProgress,
  });

  /// Export data ke format Excel
  Future<ExportResult> exportToExcel({
    required List<Map<String, dynamic>> data,
    required String filename,
    List<String>? selectedColumns,
    Map<String, List<Map<String, dynamic>>>? additionalSheets,
    Function(double)? onProgress,
  });

  /// Export user data dengan format yang sudah disesuaikan
  Future<ExportResult> exportUsers({
    required List<UserManagement> users,
    required String filename,
    required ExportFormat format,
    List<String>? selectedColumns,
    Function(double)? onProgress,
  });

  /// Export SPPG data dengan format yang sudah disesuaikan
  Future<ExportResult> exportSppgs({
    required List<Sppg> sppgs,
    required String filename,
    required ExportFormat format,
    List<String>? selectedColumns,
    Function(double)? onProgress,
  });
}

/// Format export yang didukung
enum ExportFormat {
  csv('CSV', '.csv'),
  excel('Excel', '.xlsx');

  const ExportFormat(this.displayName, this.extension);
  final String displayName;
  final String extension;
}

/// Result dari operasi export
class ExportResult {
  final bool success;
  final String? filePath;
  final String? errorMessage;
  final int? recordCount;

  const ExportResult({
    required this.success,
    this.filePath,
    this.errorMessage,
    this.recordCount,
  });

  factory ExportResult.success({
    required String filePath,
    required int recordCount,
  }) {
    return ExportResult(
      success: true,
      filePath: filePath,
      recordCount: recordCount,
    );
  }

  factory ExportResult.error(String errorMessage) {
    return ExportResult(success: false, errorMessage: errorMessage);
  }
}

/// Implementasi konkret dari ExportService
class ExportServiceImpl implements ExportService {
  @override
  Future<ExportResult> exportToCSV({
    required List<Map<String, dynamic>> data,
    required String filename,
    List<String>? selectedColumns,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      if (data.isEmpty) {
        return ExportResult.error('Tidak ada data untuk diekspor');
      }

      // Tentukan kolom yang akan diekspor
      final columns = selectedColumns ?? data.first.keys.toList();

      onProgress?.call(0.3);

      // Buat header CSV
      final csvData = <List<String>>[];
      csvData.add(columns.map((col) => _formatColumnHeader(col)).toList());

      // Tambahkan data dengan progress tracking
      for (int i = 0; i < data.length; i++) {
        final row = data[i];
        final csvRow =
            columns.map((col) => _formatCellValue(row[col])).toList();
        csvData.add(csvRow);

        // Update progress
        if (onProgress != null && i % 100 == 0) {
          final progress = 0.3 + (0.5 * (i / data.length));
          onProgress(progress);
        }
      }

      onProgress?.call(0.8);

      // Convert ke CSV string dengan proper encoding
      final csvString = const ListToCsvConverter().convert(csvData);

      // Simpan file
      final filePath = await _saveFile(
        filename: filename.endsWith('.csv') ? filename : '$filename.csv',
        content: utf8.encode(csvString),
      );

      onProgress?.call(1.0);

      return ExportResult.success(filePath: filePath, recordCount: data.length);
    } catch (e) {
      return ExportResult.error('Gagal mengekspor CSV: ${e.toString()}');
    }
  }

  @override
  Future<ExportResult> exportToExcel({
    required List<Map<String, dynamic>> data,
    required String filename,
    List<String>? selectedColumns,
    Map<String, List<Map<String, dynamic>>>? additionalSheets,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      if (data.isEmpty) {
        return ExportResult.error('Tidak ada data untuk diekspor');
      }

      final excel = Excel.createExcel();

      // Hapus sheet default
      excel.delete('Sheet1');

      onProgress?.call(0.2);

      // Buat main sheet
      await _createExcelSheet(
        excel: excel,
        sheetName: 'Data',
        data: data,
        selectedColumns: selectedColumns,
        onProgress: (progress) => onProgress?.call(0.2 + (0.6 * progress)),
      );

      // Buat additional sheets jika ada
      if (additionalSheets != null) {
        for (final entry in additionalSheets.entries) {
          await _createExcelSheet(
            excel: excel,
            sheetName: entry.key,
            data: entry.value,
            selectedColumns: selectedColumns,
          );
          // sheetIndex is not needed for this operation
        }
      }

      onProgress?.call(0.9);

      // Simpan file
      final excelBytes = excel.save();
      if (excelBytes == null) {
        return ExportResult.error('Gagal membuat file Excel');
      }

      final filePath = await _saveFile(
        filename: filename.endsWith('.xlsx') ? filename : '$filename.xlsx',
        content: Uint8List.fromList(excelBytes),
      );

      onProgress?.call(1.0);

      return ExportResult.success(filePath: filePath, recordCount: data.length);
    } catch (e) {
      return ExportResult.error('Gagal mengekspor Excel: ${e.toString()}');
    }
  }

  @override
  Future<ExportResult> exportUsers({
    required List<UserManagement> users,
    required String filename,
    required ExportFormat format,
    List<String>? selectedColumns,
    Function(double)? onProgress,
  }) async {
    // Convert users ke format yang bisa diekspor
    final data = users.map((user) => _userToExportMap(user)).toList();

    // Default columns untuk user export
    final defaultColumns = [
      'nama',
      'email',
      'telepon',
      'role',
      'status',
      'sppg_name',
      'nip',
      'alamat',
      'created_at',
      'last_login_at',
    ];

    switch (format) {
      case ExportFormat.csv:
        return exportToCSV(
          data: data,
          filename: filename,
          selectedColumns: selectedColumns ?? defaultColumns,
          onProgress: onProgress,
        );
      case ExportFormat.excel:
        return exportToExcel(
          data: data,
          filename: filename,
          selectedColumns: selectedColumns ?? defaultColumns,
          onProgress: onProgress,
        );
    }
  }

  @override
  Future<ExportResult> exportSppgs({
    required List<Sppg> sppgs,
    required String filename,
    required ExportFormat format,
    List<String>? selectedColumns,
    Function(double)? onProgress,
  }) async {
    // Convert SPPGs ke format yang bisa diekspor
    final data = sppgs.map((sppg) => _sppgToExportMap(sppg)).toList();

    // Default columns untuk SPPG export
    final defaultColumns = [
      'nama',
      'alamat',
      'status',
      'type',
      'kapasitas_harian',
      'kepala_sppg_nama',
      'perwakilan_yayasan_nama',
      'no_telepon',
      'email',
      'koordinat_lat',
      'koordinat_lng',
      'created_at',
    ];

    switch (format) {
      case ExportFormat.csv:
        return exportToCSV(
          data: data,
          filename: filename,
          selectedColumns: selectedColumns ?? defaultColumns,
          onProgress: onProgress,
        );
      case ExportFormat.excel:
        return exportToExcel(
          data: data,
          filename: filename,
          selectedColumns: selectedColumns ?? defaultColumns,
          onProgress: onProgress,
        );
    }
  }

  /// Convert UserManagement ke Map untuk export
  Map<String, dynamic> _userToExportMap(UserManagement user) {
    return {
      'nama': user.nama,
      'email': user.email,
      'telepon': user.telepon,
      'role': user.role.displayName,
      'status': user.status.displayName,
      'sppg_name': user.sppgName ?? '',
      'nip': user.nip ?? '',
      'alamat': user.alamat ?? '',
      'created_at': _formatDateTime(user.createdAt),
      'last_login_at':
          user.lastLoginAt != null ? _formatDateTime(user.lastLoginAt!) : '',
      'notes': user.notes ?? '',
    };
  }

  /// Convert Sppg ke Map untuk export
  Map<String, dynamic> _sppgToExportMap(Sppg sppg) {
    return {
      'nama': sppg.nama,
      'alamat': sppg.alamat,
      'status': sppg.status.displayName,
      'type': sppg.type.displayName,
      'kapasitas_harian': sppg.kapasitasHarian.toString(),
      'kepala_sppg_nama': sppg.kepalaSppgNama ?? '',
      'perwakilan_yayasan_nama': sppg.perwakilanYayasanNama ?? '',
      'no_telepon': sppg.noTelepon ?? '',
      'email': sppg.email ?? '',
      'koordinat_lat': sppg.koordinatLat?.toString() ?? '',
      'koordinat_lng': sppg.koordinatLng?.toString() ?? '',
      'created_at':
          sppg.createdAt != null ? _formatDateTime(sppg.createdAt!) : '',
    };
  }

  /// Format column header untuk display yang lebih baik
  String _formatColumnHeader(String column) {
    final headerMap = {
      'nama': 'Nama',
      'email': 'Email',
      'telepon': 'Nomor Telepon',
      'role': 'Peran',
      'status': 'Status',
      'sppg_name': 'SPPG',
      'nip': 'NIP',
      'alamat': 'Alamat',
      'created_at': 'Tanggal Dibuat',
      'last_login_at': 'Login Terakhir',
      'notes': 'Catatan',
      'type': 'Tipe',
      'kapasitas_harian': 'Kapasitas Harian',
      'kepala_sppg_nama': 'Kepala SPPG',
      'perwakilan_yayasan_nama': 'Perwakilan Yayasan',
      'no_telepon': 'Nomor Telepon',
      'koordinat_lat': 'Latitude',
      'koordinat_lng': 'Longitude',
    };

    return headerMap[column] ?? column.replaceAll('_', ' ').toUpperCase();
  }

  /// Format cell value untuk CSV/Excel
  String _formatCellValue(dynamic value) {
    if (value == null) return '';
    if (value is String) {
      // Handle special characters dan newlines
      return value.replaceAll('\n', ' ').replaceAll('\r', '');
    }
    return value.toString();
  }

  /// Format DateTime untuk export
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/'
        '${dateTime.month.toString().padLeft(2, '0')}/'
        '${dateTime.year} '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Buat Excel sheet dengan data
  Future<void> _createExcelSheet({
    required Excel excel,
    required String sheetName,
    required List<Map<String, dynamic>> data,
    List<String>? selectedColumns,
    Function(double)? onProgress,
  }) async {
    final sheet = excel[sheetName];
    final columns = selectedColumns ?? data.first.keys.toList();

    // Buat header dengan styling
    for (int i = 0; i < columns.length; i++) {
      final cell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0),
      );
      cell.value = TextCellValue(_formatColumnHeader(columns[i]));

      // Style header
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.blue,
        fontColorHex: ExcelColor.white,
      );
    }

    // Tambahkan data
    for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
      final row = data[rowIndex];

      for (int colIndex = 0; colIndex < columns.length; colIndex++) {
        final cell = sheet.cell(
          CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: rowIndex + 1,
          ),
        );

        final value = row[columns[colIndex]];
        if (value != null) {
          cell.value = TextCellValue(_formatCellValue(value));
        }
      }

      // Update progress
      if (onProgress != null && rowIndex % 100 == 0) {
        onProgress(rowIndex / data.length);
      }
    }

    // Auto-fit columns
    for (int i = 0; i < columns.length; i++) {
      sheet.setColumnAutoFit(i);
    }
  }

  /// Simpan file ke storage
  Future<String> _saveFile({
    required String filename,
    required Uint8List content,
  }) async {
    // Request storage permission
    if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
      final permission = await Permission.storage.request();
      if (!permission.isGranted) {
        throw Exception('Permission storage tidak diberikan');
      }
    }

    // Tentukan direktori penyimpanan
    Directory directory;
    if (kIsWeb) {
      // Untuk web, gunakan downloads
      throw UnimplementedError('Web export belum diimplementasi');
    } else if (Platform.isAndroid) {
      directory = Directory('/storage/emulated/0/Download');
      if (!await directory.exists()) {
        directory =
            await getExternalStorageDirectory() ??
            await getApplicationDocumentsDirectory();
      }
    } else if (Platform.isIOS) {
      directory = await getApplicationDocumentsDirectory();
    } else {
      // Desktop platforms
      directory =
          await getDownloadsDirectory() ??
          await getApplicationDocumentsDirectory();
    }

    // Buat file dengan nama unik jika sudah ada
    String finalFilename = filename;
    int counter = 1;
    while (await File('${directory.path}/$finalFilename').exists()) {
      final nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
      final extension = filename.substring(filename.lastIndexOf('.'));
      finalFilename = '${nameWithoutExt}_$counter$extension';
      counter++;
    }

    final file = File('${directory.path}/$finalFilename');
    await file.writeAsBytes(content);

    return file.path;
  }
}
