import '../../domain/entities/activity_event.dart';

/// Data model for activity events from Supabase
class ActivityEventModel extends ActivityEvent {
  const ActivityEventModel({
    required super.id,
    required super.title,
    required super.description,
    super.sppgId,
    required super.sppgName,
    required super.type,
    required super.timestamp,
    required super.severity,
    super.userId,
    super.userName,
    super.data,
    super.isAcknowledged,
    super.relatedEntityId,
    super.tags,
    super.visibleToRoles,
  });

  /// Create model from JSON data
  factory ActivityEventModel.fromJson(Map<String, dynamic> json) {
    return ActivityEventModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      sppgId: json['sppg_id'] as String?,
      sppgName: json['sppg_name'] as String,
      type: ActivityType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ActivityType.systemMaintenance,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      severity: ActivitySeverity.values.firstWhere(
        (e) => e.name == json['severity'],
        orElse: () => ActivitySeverity.info,
      ),
      userId: json['user_id'] as String?,
      userName: json['user_name'] as String?,
      data: json['data'] as Map<String, dynamic>?,
      isAcknowledged: json['is_acknowledged'] as bool? ?? false,
      relatedEntityId: json['related_entity_id'] as String?,
      tags:
          json['tags'] != null ? List<String>.from(json['tags'] as List) : null,
      visibleToRoles:
          json['visible_to_roles'] != null
              ? List<String>.from(json['visible_to_roles'] as List)
              : const ['admin_yayasan'],
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'sppg_id': sppgId,
      'sppg_name': sppgName,
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
      'severity': severity.name,
      'user_id': userId,
      'user_name': userName,
      'data': data,
      'is_acknowledged': isAcknowledged,
      'related_entity_id': relatedEntityId,
      'tags': tags,
      'visible_to_roles': visibleToRoles,
    };
  }
}
