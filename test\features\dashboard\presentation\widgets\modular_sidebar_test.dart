import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/modular_sidebar.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/dashboard_configuration.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/cubit/navigation_cubit.dart';

void main() {
  group('ModularSidebar', () {
    late NavigationConfiguration testConfig;

    setUp(() {
      testConfig = const NavigationConfiguration(
        sections: [
          NavigationSection(
            title: 'System Management',
            items: [
              NavigationItem(
                title: 'SPPG Management',
                route: '/admin/sppg',
                icon: FluentIcons.home,
                requiredPermissions: ['admin.sppg.manage'],
              ),
              NavigationItem(
                title: 'User Management',
                route: '/admin/users',
                icon: FluentIcons.people,
                requiredPermissions: ['admin.users.manage'],
                badgeCount: 3,
              ),
            ],
          ),
          NavigationSection(
            title: 'Monitoring',
            items: [
              NavigationItem(
                title: 'Reports',
                route: '/admin/reports',
                icon: FluentIcons.chart,
                requiredPermissions: ['admin.reports.view'],
              ),
            ],
          ),
        ],
      );
    });

    testWidgets('renders sidebar with navigation sections', (tester) async {
      // Create a NavigationCubit for testing
      final navigationCubit = NavigationCubit();

      await tester.pumpWidget(
        FluentApp(
          home: BlocProvider<NavigationCubit>(
            create: (context) => navigationCubit,
            child: Scaffold(body: ModularSidebar(configuration: testConfig)),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Note: The actual content depends on AuthService.instance.currentUser
      // Since we don't have a proper user setup, the widget might show empty content
      // This test mainly verifies that the widget builds without errors
      expect(find.byType(ModularSidebar), findsOneWidget);
    });

    testWidgets('builds without errors when no user is authenticated', (
      tester,
    ) async {
      final navigationCubit = NavigationCubit();

      await tester.pumpWidget(
        FluentApp(
          home: BlocProvider<NavigationCubit>(
            create: (context) => navigationCubit,
            child: Scaffold(body: ModularSidebar(configuration: testConfig)),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // When no user is authenticated, the sidebar should show empty content
      expect(find.byType(ModularSidebar), findsOneWidget);
    });

    testWidgets('respects configuration parameters', (tester) async {
      final navigationCubit = NavigationCubit();

      await tester.pumpWidget(
        FluentApp(
          home: BlocProvider<NavigationCubit>(
            create: (context) => navigationCubit,
            child: Scaffold(
              body: ModularSidebar(
                configuration: testConfig,
                isCollapsible: false,
                defaultCollapsed: true,
                expandedWidth: 300.0,
                collapsedWidth: 80.0,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(ModularSidebar), findsOneWidget);
    });

    testWidgets('handles null configuration gracefully', (tester) async {
      final navigationCubit = NavigationCubit();

      await tester.pumpWidget(
        FluentApp(
          home: BlocProvider<NavigationCubit>(
            create: (context) => navigationCubit,
            child: Scaffold(
              body: ModularSidebar(
                configuration: null, // Test with null configuration
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(ModularSidebar), findsOneWidget);
    });

    testWidgets('accepts onToggle callback parameter', (tester) async {
      final navigationCubit = NavigationCubit();

      await tester.pumpWidget(
        FluentApp(
          home: BlocProvider<NavigationCubit>(
            create: (context) => navigationCubit,
            child: Scaffold(
              body: ModularSidebar(
                configuration: testConfig,
                onToggle: () {
                  // Callback provided but not tested here
                  // Testing actual toggle would require proper user authentication setup
                },
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(ModularSidebar), findsOneWidget);
    });
  });
}
