import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:fluent_ui/fluent_ui.dart';
import '../../domain/entities/entities.dart';

part 'dashboard_configuration_model.g.dart';

/// Data model for dashboard configuration with JSON serialization
@JsonSerializable(explicitToJson: true)
class DashboardConfigurationModel extends Equatable {
  /// Unique identifier for the user role
  @Json<PERSON>ey(name: 'role_id')
  final String roleId;

  /// List of components to display on this dashboard
  final List<ComponentConfigModel> components;

  /// Layout configuration for responsive design
  final LayoutConfigurationModel layout;

  /// Navigation configuration for sidebar
  final NavigationConfigurationModel navigation;

  /// Theme preferences specific to this role
  final ThemeConfigurationModel? theme;

  /// Configuration version for migration support
  @JsonKey(name: 'config_version', defaultValue: '1.0.0')
  final String configVersion;

  /// Configuration metadata
  final Map<String, dynamic>? metadata;

  /// Creation timestamp
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  /// Last modification timestamp
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  const DashboardConfigurationModel({
    required this.roleId,
    required this.components,
    required this.layout,
    required this.navigation,
    this.theme,
    this.configVersion = '1.0.0',
    this.metadata,
    this.createdAt,
    this.updatedAt,
  });

  /// Create from JSON
  factory DashboardConfigurationModel.fromJson(Map<String, dynamic> json) =>
      _$DashboardConfigurationModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$DashboardConfigurationModelToJson(this);

  /// Convert to domain entity
  DashboardConfiguration toDomain() {
    return DashboardConfiguration(
      roleId: roleId,
      components: components.map((c) => c.toDomain()).toList(),
      layout: layout.toDomain(),
      navigation: navigation.toDomain(),
      theme: theme?.toDomain(),
    );
  }

  /// Create from domain entity
  factory DashboardConfigurationModel.fromDomain(
    DashboardConfiguration domain, {
    String? configVersion,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DashboardConfigurationModel(
      roleId: domain.roleId,
      components:
          domain.components
              .map((c) => ComponentConfigModel.fromDomain(c))
              .toList(),
      layout: LayoutConfigurationModel.fromDomain(domain.layout),
      navigation: NavigationConfigurationModel.fromDomain(domain.navigation),
      theme:
          domain.theme != null
              ? ThemeConfigurationModel.fromDomain(domain.theme!)
              : null,
      configVersion: configVersion ?? '1.0.0',
      metadata: metadata,
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Copy with updated fields
  DashboardConfigurationModel copyWith({
    String? roleId,
    List<ComponentConfigModel>? components,
    LayoutConfigurationModel? layout,
    NavigationConfigurationModel? navigation,
    ThemeConfigurationModel? theme,
    String? configVersion,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DashboardConfigurationModel(
      roleId: roleId ?? this.roleId,
      components: components ?? this.components,
      layout: layout ?? this.layout,
      navigation: navigation ?? this.navigation,
      theme: theme ?? this.theme,
      configVersion: configVersion ?? this.configVersion,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
    roleId,
    components,
    layout,
    navigation,
    theme,
    configVersion,
    metadata,
    createdAt,
    updatedAt,
  ];
}

/// Data model for component configuration
@JsonSerializable(explicitToJson: true)
class ComponentConfigModel extends Equatable {
  /// Unique identifier for the component type
  @JsonKey(name: 'component_id')
  final String componentId;

  /// Display title for the component
  final String title;

  /// Component-specific parameters
  final Map<String, dynamic> parameters;

  /// Position and size in the grid layout
  final GridPositionModel position;

  /// Required permissions to view this component
  @JsonKey(name: 'required_permissions', defaultValue: <String>[])
  final List<String> requiredPermissions;

  /// Whether component should refresh automatically
  @JsonKey(name: 'auto_refresh', defaultValue: false)
  final bool autoRefresh;

  /// Refresh interval in seconds (if autoRefresh is true)
  @JsonKey(name: 'refresh_interval_seconds', defaultValue: 30)
  final int refreshIntervalSeconds;

  /// Component enabled state
  @JsonKey(defaultValue: true)
  final bool enabled;

  /// Component visibility conditions
  @JsonKey(name: 'visibility_conditions')
  final Map<String, dynamic>? visibilityConditions;

  const ComponentConfigModel({
    required this.componentId,
    required this.title,
    required this.parameters,
    required this.position,
    this.requiredPermissions = const [],
    this.autoRefresh = false,
    this.refreshIntervalSeconds = 30,
    this.enabled = true,
    this.visibilityConditions,
  });

  /// Create from JSON
  factory ComponentConfigModel.fromJson(Map<String, dynamic> json) =>
      _$ComponentConfigModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$ComponentConfigModelToJson(this);

  /// Convert to domain entity
  ComponentConfig toDomain() {
    return ComponentConfig(
      componentId: componentId,
      title: title,
      parameters: parameters,
      position: position.toDomain(),
      requiredPermissions: requiredPermissions,
      autoRefresh: autoRefresh,
      refreshIntervalSeconds: refreshIntervalSeconds,
    );
  }

  /// Create from domain entity
  factory ComponentConfigModel.fromDomain(ComponentConfig domain) {
    return ComponentConfigModel(
      componentId: domain.componentId,
      title: domain.title,
      parameters: domain.parameters,
      position: GridPositionModel.fromDomain(domain.position),
      requiredPermissions: domain.requiredPermissions,
      autoRefresh: domain.autoRefresh,
      refreshIntervalSeconds: domain.refreshIntervalSeconds,
    );
  }

  @override
  List<Object?> get props => [
    componentId,
    title,
    parameters,
    position,
    requiredPermissions,
    autoRefresh,
    refreshIntervalSeconds,
    enabled,
    visibilityConditions,
  ];
}

/// Data model for grid position
@JsonSerializable()
class GridPositionModel extends Equatable {
  /// Column index (0-based)
  final int column;

  /// Row index (0-based)
  final int row;

  /// Number of columns to span
  @JsonKey(name: 'column_span', defaultValue: 1)
  final int columnSpan;

  /// Number of rows to span
  @JsonKey(name: 'row_span', defaultValue: 1)
  final int rowSpan;

  /// Minimum height for the component
  @JsonKey(name: 'min_height')
  final double? minHeight;

  /// Maximum height for the component
  @JsonKey(name: 'max_height')
  final double? maxHeight;

  const GridPositionModel({
    required this.column,
    required this.row,
    this.columnSpan = 1,
    this.rowSpan = 1,
    this.minHeight,
    this.maxHeight,
  });

  /// Create from JSON
  factory GridPositionModel.fromJson(Map<String, dynamic> json) =>
      _$GridPositionModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$GridPositionModelToJson(this);

  /// Convert to domain entity
  GridPosition toDomain() {
    return GridPosition(
      column: column,
      row: row,
      columnSpan: columnSpan,
      rowSpan: rowSpan,
      minHeight: minHeight,
      maxHeight: maxHeight,
    );
  }

  /// Create from domain entity
  factory GridPositionModel.fromDomain(GridPosition domain) {
    return GridPositionModel(
      column: domain.column,
      row: domain.row,
      columnSpan: domain.columnSpan,
      rowSpan: domain.rowSpan,
      minHeight: domain.minHeight,
      maxHeight: domain.maxHeight,
    );
  }

  @override
  List<Object?> get props => [
    column,
    row,
    columnSpan,
    rowSpan,
    minHeight,
    maxHeight,
  ];
}

/// Data model for layout configuration
@JsonSerializable()
class LayoutConfigurationModel extends Equatable {
  /// Number of columns for desktop layout
  @JsonKey(name: 'desktop_columns', defaultValue: 4)
  final int desktopColumns;

  /// Number of columns for tablet layout
  @JsonKey(name: 'tablet_columns', defaultValue: 2)
  final int tabletColumns;

  /// Number of columns for mobile layout
  @JsonKey(name: 'mobile_columns', defaultValue: 1)
  final int mobileColumns;

  /// Spacing between components
  @JsonKey(defaultValue: 16.0)
  final double spacing;

  /// Responsive breakpoints configuration
  @JsonKey(name: 'responsive_breakpoints')
  final ResponsiveBreakpointsModel? responsiveBreakpoints;

  const LayoutConfigurationModel({
    this.desktopColumns = 4,
    this.tabletColumns = 2,
    this.mobileColumns = 1,
    this.spacing = 16.0,
    this.responsiveBreakpoints,
  });

  /// Create from JSON
  factory LayoutConfigurationModel.fromJson(Map<String, dynamic> json) =>
      _$LayoutConfigurationModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$LayoutConfigurationModelToJson(this);

  /// Convert to domain entity
  LayoutConfiguration toDomain() {
    return LayoutConfiguration(
      desktopColumns: desktopColumns,
      tabletColumns: tabletColumns,
      mobileColumns: mobileColumns,
      spacing: spacing,
      padding: const EdgeInsets.all(16.0), // Default padding
      breakpoints:
          responsiveBreakpoints != null
              ? responsiveBreakpoints!.toDomain()
              : const ResponsiveBreakpoints(),
    );
  }

  /// Create from domain entity
  factory LayoutConfigurationModel.fromDomain(LayoutConfiguration domain) {
    return LayoutConfigurationModel(
      desktopColumns: domain.desktopColumns,
      tabletColumns: domain.tabletColumns,
      mobileColumns: domain.mobileColumns,
      spacing: domain.spacing,
      responsiveBreakpoints: ResponsiveBreakpointsModel.fromDomain(
        domain.breakpoints,
      ),
    );
  }

  @override
  List<Object?> get props => [
    desktopColumns,
    tabletColumns,
    mobileColumns,
    spacing,
    responsiveBreakpoints,
  ];
}

/// Data model for responsive breakpoints
@JsonSerializable()
class ResponsiveBreakpointsModel extends Equatable {
  @JsonKey(name: 'tablet', defaultValue: 768.0)
  final double tablet;

  @JsonKey(name: 'desktop', defaultValue: 1024.0)
  final double desktop;

  const ResponsiveBreakpointsModel({
    this.tablet = 768.0,
    this.desktop = 1024.0,
  });

  /// Create from JSON
  factory ResponsiveBreakpointsModel.fromJson(Map<String, dynamic> json) =>
      _$ResponsiveBreakpointsModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$ResponsiveBreakpointsModelToJson(this);

  /// Convert to domain entity
  ResponsiveBreakpoints toDomain() {
    return ResponsiveBreakpoints(tablet: tablet, desktop: desktop);
  }

  /// Create from domain entity
  factory ResponsiveBreakpointsModel.fromDomain(ResponsiveBreakpoints domain) {
    return ResponsiveBreakpointsModel(
      tablet: domain.tablet,
      desktop: domain.desktop,
    );
  }

  @override
  List<Object?> get props => [tablet, desktop];
}

/// Data model for navigation configuration
@JsonSerializable()
class NavigationConfigurationModel extends Equatable {
  /// Navigation sections
  final List<NavigationSectionModel> sections;

  /// Whether sidebar is collapsible on mobile
  @JsonKey(name: 'is_collapsible', defaultValue: true)
  final bool isCollapsible;

  /// Default collapsed state
  @JsonKey(name: 'default_collapsed', defaultValue: false)
  final bool defaultCollapsed;

  /// Width of the expanded sidebar
  @JsonKey(name: 'expanded_width', defaultValue: 240.0)
  final double expandedWidth;

  /// Width of the collapsed sidebar
  @JsonKey(name: 'collapsed_width', defaultValue: 56.0)
  final double collapsedWidth;

  const NavigationConfigurationModel({
    required this.sections,
    this.isCollapsible = true,
    this.defaultCollapsed = false,
    this.expandedWidth = 240.0,
    this.collapsedWidth = 56.0,
  });

  /// Create from JSON
  factory NavigationConfigurationModel.fromJson(Map<String, dynamic> json) =>
      _$NavigationConfigurationModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$NavigationConfigurationModelToJson(this);

  /// Convert to domain entity
  NavigationConfiguration toDomain() {
    return NavigationConfiguration(
      sections: sections.map((section) => section.toDomain()).toList(),
      isCollapsible: isCollapsible,
      defaultCollapsed: defaultCollapsed,
      expandedWidth: expandedWidth,
      collapsedWidth: collapsedWidth,
    );
  }

  /// Create from domain entity
  factory NavigationConfigurationModel.fromDomain(
    NavigationConfiguration domain,
  ) {
    return NavigationConfigurationModel(
      sections:
          domain.sections
              .map((section) => NavigationSectionModel.fromDomain(section))
              .toList(),
      isCollapsible: domain.isCollapsible,
      defaultCollapsed: domain.defaultCollapsed,
      expandedWidth: domain.expandedWidth,
      collapsedWidth: domain.collapsedWidth,
    );
  }

  @override
  List<Object?> get props => [
    sections,
    isCollapsible,
    defaultCollapsed,
    expandedWidth,
    collapsedWidth,
  ];
}

/// Data model for navigation section
@JsonSerializable()
class NavigationSectionModel extends Equatable {
  /// Section title
  final String title;

  /// Navigation items in this section
  final List<NavigationItemModel> items;

  /// Required permissions to see this section
  @JsonKey(name: 'required_permissions', defaultValue: <String>[])
  final List<String> requiredPermissions;

  /// Whether section is collapsible
  @JsonKey(name: 'is_collapsible', defaultValue: false)
  final bool isCollapsible;

  /// Default expanded state
  @JsonKey(name: 'default_expanded', defaultValue: true)
  final bool defaultExpanded;

  const NavigationSectionModel({
    required this.title,
    required this.items,
    this.requiredPermissions = const [],
    this.isCollapsible = false,
    this.defaultExpanded = true,
  });

  /// Create from JSON
  factory NavigationSectionModel.fromJson(Map<String, dynamic> json) =>
      _$NavigationSectionModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$NavigationSectionModelToJson(this);

  /// Convert to domain entity
  NavigationSection toDomain() {
    return NavigationSection(
      title: title,
      items: items.map((item) => item.toDomain()).toList(),
      requiredPermissions: requiredPermissions,
      isCollapsible: isCollapsible,
      defaultExpanded: defaultExpanded,
    );
  }

  /// Create from domain entity
  factory NavigationSectionModel.fromDomain(NavigationSection domain) {
    return NavigationSectionModel(
      title: domain.title,
      items:
          domain.items
              .map((item) => NavigationItemModel.fromDomain(item))
              .toList(),
      requiredPermissions: domain.requiredPermissions,
      isCollapsible: domain.isCollapsible,
      defaultExpanded: domain.defaultExpanded,
    );
  }

  @override
  List<Object?> get props => [title, items];
}

/// Data model for navigation item
@JsonSerializable()
class NavigationItemModel extends Equatable {
  /// Item title
  final String title;

  /// Route to navigate to
  final String route;

  /// Icon code point
  @JsonKey(name: 'icon_code_point')
  final int? iconCodePoint;

  /// Required permissions to access this item
  @JsonKey(name: 'required_permissions')
  final List<String> requiredPermissions;

  /// Badge count
  @JsonKey(name: 'badge_count')
  final int? badgeCount;

  /// Badge color
  @JsonKey(name: 'badge_color')
  final String? badgeColor;

  const NavigationItemModel({
    required this.title,
    required this.route,
    this.iconCodePoint,
    required this.requiredPermissions,
    this.badgeCount,
    this.badgeColor,
  });

  /// Create from JSON
  factory NavigationItemModel.fromJson(Map<String, dynamic> json) =>
      _$NavigationItemModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$NavigationItemModelToJson(this);

  /// Convert to domain entity
  NavigationItem toDomain() {
    return NavigationItem(
      title: title,
      route: route,
      icon:
          iconCodePoint != null
              ? IconData(iconCodePoint!, fontFamily: 'FluentIcons')
              : null,
      requiredPermissions: requiredPermissions,
      badgeCount: badgeCount,
      badgeColor: badgeColor != null ? Color(int.parse(badgeColor!)) : null,
    );
  }

  /// Create from domain entity
  factory NavigationItemModel.fromDomain(NavigationItem domain) {
    return NavigationItemModel(
      title: domain.title,
      route: domain.route,
      iconCodePoint: domain.icon?.codePoint,
      requiredPermissions: domain.requiredPermissions,
      badgeCount: domain.badgeCount,
      badgeColor: domain.badgeColor?.toARGB32().toString(),
    );
  }

  @override
  List<Object?> get props => [
    title,
    route,
    iconCodePoint,
    requiredPermissions,
    badgeCount,
    badgeColor,
  ];
}

/// Data model for theme configuration
@JsonSerializable()
class ThemeConfigurationModel extends Equatable {
  /// Accent color configuration (primary color)
  @JsonKey(name: 'accent_color')
  final String? accentColor;

  /// Card color configuration
  @JsonKey(name: 'card_color')
  final String? cardColor;

  /// Text style configurations
  @JsonKey(name: 'text_styles')
  final Map<String, TextStyleModel>? textStyles;

  const ThemeConfigurationModel({
    this.accentColor,
    this.cardColor,
    this.textStyles,
  });

  /// Create from JSON
  factory ThemeConfigurationModel.fromJson(Map<String, dynamic> json) =>
      _$ThemeConfigurationModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$ThemeConfigurationModelToJson(this);

  /// Convert to domain entity
  ThemeConfiguration toDomain() {
    return ThemeConfiguration(
      accentColor: accentColor != null ? Color(int.parse(accentColor!)) : null,
      cardColor: cardColor != null ? Color(int.parse(cardColor!)) : null,
      textStyles: textStyles?.map(
        (key, value) => MapEntry(key, value.toDomain()),
      ),
    );
  }

  /// Create from domain entity
  factory ThemeConfigurationModel.fromDomain(ThemeConfiguration domain) {
    return ThemeConfigurationModel(
      accentColor: domain.accentColor?.toARGB32().toString(),
      cardColor: domain.cardColor?.toARGB32().toString(),
      textStyles: domain.textStyles?.map(
        (key, value) => MapEntry(key, TextStyleModel.fromDomain(value)),
      ),
    );
  }

  @override
  List<Object?> get props => [accentColor, cardColor, textStyles];
}

/// Data model for text style
@JsonSerializable()
class TextStyleModel extends Equatable {
  @JsonKey(name: 'font_size')
  final double? fontSize;

  @JsonKey(name: 'font_weight')
  final String? fontWeight;

  final String? color;

  const TextStyleModel({this.fontSize, this.fontWeight, this.color});

  /// Create from JSON
  factory TextStyleModel.fromJson(Map<String, dynamic> json) =>
      _$TextStyleModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$TextStyleModelToJson(this);

  /// Convert to domain entity
  TextStyle toDomain() {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: _parseFontWeight(fontWeight),
      color: color != null ? Color(int.parse(color!)) : null,
    );
  }

  /// Create from domain entity
  factory TextStyleModel.fromDomain(TextStyle domain) {
    return TextStyleModel(
      fontSize: domain.fontSize,
      fontWeight: _fontWeightToString(domain.fontWeight),
      color: domain.color?.toARGB32().toString(),
    );
  }

  /// Parse font weight string to FontWeight
  static FontWeight? _parseFontWeight(String? weight) {
    if (weight == null) return null;

    switch (weight.toLowerCase()) {
      case 'thin':
        return FontWeight.w100;
      case 'extralight':
        return FontWeight.w200;
      case 'light':
        return FontWeight.w300;
      case 'regular':
        return FontWeight.w400;
      case 'medium':
        return FontWeight.w500;
      case 'semibold':
        return FontWeight.w600;
      case 'bold':
        return FontWeight.w700;
      case 'extrabold':
        return FontWeight.w800;
      case 'black':
        return FontWeight.w900;
      default:
        return FontWeight.normal;
    }
  }

  /// Convert FontWeight to string
  static String? _fontWeightToString(FontWeight? weight) {
    if (weight == null) return null;

    if (weight == FontWeight.w100) return 'thin';
    if (weight == FontWeight.w200) return 'extralight';
    if (weight == FontWeight.w300) return 'light';
    if (weight == FontWeight.w400) return 'regular';
    if (weight == FontWeight.w500) return 'medium';
    if (weight == FontWeight.w600) return 'semibold';
    if (weight == FontWeight.w700) return 'bold';
    if (weight == FontWeight.w800) return 'extrabold';
    if (weight == FontWeight.w900) return 'black';

    return 'regular';
  }

  @override
  List<Object?> get props => [fontSize, fontWeight, color];
}
