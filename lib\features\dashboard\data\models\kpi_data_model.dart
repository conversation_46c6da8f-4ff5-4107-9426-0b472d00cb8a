import 'package:equatable/equatable.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/entities.dart';

part 'kpi_data_model.g.dart';

/// Data model for KPI information with JSON serialization
@JsonSerializable()
class KPIDataModel extends Equatable {
  /// Unique identifier for the KPI
  final String id;

  /// Display title for the KPI
  final String title;

  /// Main value to display
  final String value;

  /// Subtitle or description
  final String subtitle;

  /// Icon code point for serialization
  @JsonKey(name: 'icon_code_point')
  final int iconCodePoint;

  /// Icon font family
  @JsonKey(name: 'icon_font_family')
  final String? iconFontFamily;

  /// Background color as hex string
  @JsonKey(name: 'background_color')
  final String backgroundColor;

  /// Icon color as hex string
  @JsonKey(name: 'icon_color')
  final String iconColor;

  /// Text color for the value as hex string
  @J<PERSON><PERSON><PERSON>(name: 'value_color')
  final String? valueColor;

  /// Text color for the title as hex string
  @Json<PERSON>ey(name: 'title_color')
  final String? titleColor;

  /// Trend information (optional)
  final KPITrendModel? trend;

  /// Additional metadata
  final Map<String, dynamic>? metadata;

  /// Last update timestamp
  @JsonKey(name: 'last_updated')
  final DateTime lastUpdated;

  const KPIDataModel({
    required this.id,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.iconCodePoint,
    this.iconFontFamily,
    required this.backgroundColor,
    required this.iconColor,
    this.valueColor,
    this.titleColor,
    this.trend,
    this.metadata,
    required this.lastUpdated,
  });

  /// Create from JSON
  factory KPIDataModel.fromJson(Map<String, dynamic> json) =>
      _$KPIDataModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$KPIDataModelToJson(this);

  /// Convert to domain entity
  KPIData toDomain() {
    return KPIData(
      id: id,
      title: title,
      value: value,
      subtitle: subtitle,
      icon: IconData(iconCodePoint, fontFamily: iconFontFamily),
      backgroundColor: _parseColor(backgroundColor),
      iconColor: _parseColor(iconColor),
      valueColor: valueColor != null ? _parseColor(valueColor!) : null,
      titleColor: titleColor != null ? _parseColor(titleColor!) : null,
      trend: trend?.toDomain(),
      metadata: metadata,
      lastUpdated: lastUpdated,
    );
  }

  /// Create from domain entity
  factory KPIDataModel.fromDomain(KPIData domain) {
    return KPIDataModel(
      id: domain.id,
      title: domain.title,
      value: domain.value,
      subtitle: domain.subtitle,
      iconCodePoint: domain.icon.codePoint,
      iconFontFamily: domain.icon.fontFamily,
      backgroundColor: _colorToHex(domain.backgroundColor),
      iconColor: _colorToHex(domain.iconColor),
      valueColor:
          domain.valueColor != null ? _colorToHex(domain.valueColor!) : null,
      titleColor:
          domain.titleColor != null ? _colorToHex(domain.titleColor!) : null,
      trend:
          domain.trend != null ? KPITrendModel.fromDomain(domain.trend!) : null,
      metadata: domain.metadata,
      lastUpdated: domain.lastUpdated,
    );
  }

  /// Parse color from hex string
  static Color _parseColor(String hexColor) {
    final hex = hexColor.replaceAll('#', '');
    if (hex.length == 6) {
      return Color(int.parse('FF$hex', radix: 16));
    } else if (hex.length == 8) {
      return Color(int.parse(hex, radix: 16));
    }
    return Colors.grey;
  }

  /// Convert color to hex string
  static String _colorToHex(Color color) {
    return '#${color.toARGB32().toRadixString(16).padLeft(8, '0').toUpperCase()}';
  }

  @override
  List<Object?> get props => [
    id,
    title,
    value,
    subtitle,
    iconCodePoint,
    iconFontFamily,
    backgroundColor,
    iconColor,
    valueColor,
    titleColor,
    trend,
    metadata,
    lastUpdated,
  ];
}

/// Data model for KPI trend information
@JsonSerializable()
class KPITrendModel extends Equatable {
  /// Trend direction
  final String direction;

  /// Percentage change
  final double percentage;

  /// Comparison period
  final String period;

  /// Whether the trend is considered positive
  @JsonKey(name: 'is_positive')
  final bool isPositive;

  const KPITrendModel({
    required this.direction,
    required this.percentage,
    required this.period,
    required this.isPositive,
  });

  /// Create from JSON
  factory KPITrendModel.fromJson(Map<String, dynamic> json) =>
      _$KPITrendModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$KPITrendModelToJson(this);

  /// Convert to domain entity
  KPITrend toDomain() {
    return KPITrend(
      direction: _parseTrendDirection(direction),
      percentage: percentage,
      period: period,
      isPositive: isPositive,
    );
  }

  /// Create from domain entity
  factory KPITrendModel.fromDomain(KPITrend domain) {
    return KPITrendModel(
      direction: domain.direction.name,
      percentage: domain.percentage,
      period: domain.period,
      isPositive: domain.isPositive,
    );
  }

  /// Parse trend direction from string
  static TrendDirection _parseTrendDirection(String direction) {
    switch (direction.toLowerCase()) {
      case 'up':
        return TrendDirection.up;
      case 'down':
        return TrendDirection.down;
      case 'stable':
        return TrendDirection.stable;
      default:
        return TrendDirection.stable;
    }
  }

  @override
  List<Object?> get props => [direction, percentage, period, isPositive];
}
