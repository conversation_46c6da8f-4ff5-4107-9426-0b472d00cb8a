// SPPG Management Page untuk Admin Yayasan

import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../app/constants/app_colors.dart';
import '../../../../../app/constants/app_spacing.dart';
import '../../../../../app/constants/app_typography.dart';
import '../../../../../app/constants/app_breakpoints.dart';
import '../../../../../app/widgets/app_card.dart';
import '../../../../../app/widgets/app_button.dart';
import '../../../../../app/widgets/responsive_layout.dart';
import '../../../../../app/widgets/stats_card_widget.dart';
import '../../../../../app/services/statistics_service.dart';
import '../../../../../app/services/export_service.dart';
import '../../../../../app/widgets/form_dialog.dart';
import '../../../../../core/utils/connection_manager.dart' as core_connection;
import '../../data/repositories/sppg_supabase_repository.dart';
import '../../domain/models/sppg.dart';
import '../cubit/sppg_cubit.dart';
import '../widgets/sppg_form_widget.dart';
import '../widgets/sppg_table_widget.dart';

/// Halaman manajemen SPPG untuk Admin Yayasan
/// Menampilkan daftar SPPG dengan fitur pencarian, filter, dan CRUD operations
class SppgManagementPage extends StatefulWidget {
  const SppgManagementPage({super.key});

  @override
  State<SppgManagementPage> createState() => _SppgManagementPageState();
}

class _SppgManagementPageState extends State<SppgManagementPage> {
  final TextEditingController _searchController = TextEditingController();
  SppgStatus? _selectedStatusFilter;
  SppgType? _selectedTypeFilter;
  List<Sppg> _sppgs = [];

  @override
  void initState() {
    super.initState();
    _loadData(); // Load data when the widget is initialized
    _initializeStatistics(); // Initialize statistics service
  }

  Future<void> _initializeStatistics() async {
    try {
      await StatisticsService.instance.initialize();
    } catch (e) {
      // Handle initialization error silently - the UI will show error states
    }
  }

  Future<void> _loadData() async {
    // Replace this with your actual data loading logic
    // For example, fetch data from a repository or API
    setState(() {
      _sppgs = [
        Sppg(
          id: '1',
          nama: 'SPPG Melati',
          alamat: 'Alamat Melati',
          status: SppgStatus.aktif,
          type: SppgType.milikYayasan,
          kapasitasHarian: 100,
        ),
        Sppg(
          id: '2',
          nama: 'SPPG Mawar',
          alamat: 'Alamat Mawar',
          status: SppgStatus.nonAktif,
          type: SppgType.mitra,
          kapasitasHarian: 50,
        ),
      ];
    });
  }

  void _onSearchChanged(String query) {
    context.read<SppgCubit>().searchSppg(query);
  }

  void _onStatusFilterChanged(SppgStatus? status) {
    setState(() {
      _selectedStatusFilter = status;
    });
    context.read<SppgCubit>().filterByStatus(status);
  }

  void _onTypeFilterChanged(SppgType? type) {
    setState(() {
      _selectedTypeFilter = type;
    });
    context.read<SppgCubit>().filterByType(type);
  }

  void _clearFilters() {
    setState(() {
      _selectedStatusFilter = null;
      _selectedTypeFilter = null;
      _searchController.clear();
    });
    context.read<SppgCubit>().clearFilters();
  }

  List<PerwakilanYayasan> _getAvailablePerwakilanYayasan() {
    // TODO: Replace with actual data from UserManagementCubit
    // This should fetch users with role 'perwakilanYayasan'
    return [
      PerwakilanYayasan(
        id: 'perwakilan_001',
        nama: 'Ahmad Suryadi',
        email: '<EMAIL>',
      ),
      PerwakilanYayasan(
        id: 'perwakilan_002',
        nama: 'Siti Nurhaliza',
        email: '<EMAIL>',
      ),
      PerwakilanYayasan(
        id: 'perwakilan_003',
        nama: 'Budi Santoso',
        email: '<EMAIL>',
      ),
    ];
  }

  void _showAddSppgDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return FormDialog(
          config: const FormDialogConfig(
            title: 'Tambah SPPG Baru',
            icon: FluentIcons.add,
          ),
          child: SppgFormWidget(
            onSave: (sppg) async {
              await context.read<SppgCubit>().createSppg(sppg);
              if (context.mounted) {
                Navigator.pop(context); // Close dialog on save
              }
              _loadData(); // Refresh data after creation
            },
            onCancel: () => Navigator.pop(context),
            availablePerwakilanYayasan: _getAvailablePerwakilanYayasan(),
          ),
        );
      },
    );
  }

  void _showEditSppgDialog(Sppg sppg) {
    showDialog(
      context: context,
      builder: (context) {
        return FormDialog(
          config: FormDialogConfig(
            title: 'Edit SPPG: ${sppg.nama}',
            icon: FluentIcons.edit,
          ),
          child: SppgFormWidget(
            initialData: sppg,
            onSave: (updatedSppg) async {
              await context.read<SppgCubit>().updateSppg(updatedSppg);
              if (context.mounted) {
                Navigator.pop(context); // Close dialog on save
              }
              _loadData(); // Refresh data after update
            },
            onCancel: () => Navigator.pop(context),
            availablePerwakilanYayasan: _getAvailablePerwakilanYayasan(),
          ),
        );
      },
    );
  }

  void _confirmDeleteSppg(Sppg sppg) {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Konfirmasi Hapus'),
            content: Text(
              'Apakah Anda yakin ingin menghapus SPPG "${sppg.nama}"? Tindakan ini tidak dapat dibatalkan.',
            ),
            actions: [
              Button(
                child: const Text('Batal'),
                onPressed: () => Navigator.pop(context),
              ),
              FilledButton(
                style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(AppColors.errorRed),
                ),
                onPressed: () {
                  Navigator.pop(context);
                  context.read<SppgCubit>().deleteSppg(sppg.id);
                },
                child: const Text('Hapus'),
              ),
            ],
          ),
    );
  }

  /// Export SPPG data to CSV
  Future<void> _exportToCSV() async {
    try {
      _showExportProgressDialog(context, 'CSV');

      // Get filtered SPPGs based on current filters
      List<Sppg> sppgsToExport = _sppgs;

      // Apply current filters
      if (_selectedStatusFilter != null) {
        sppgsToExport =
            sppgsToExport
                .where((sppg) => sppg.status == _selectedStatusFilter)
                .toList();
      }

      if (_selectedTypeFilter != null) {
        sppgsToExport =
            sppgsToExport
                .where((sppg) => sppg.type == _selectedTypeFilter)
                .toList();
      }

      final searchQuery = _searchController.text.trim();
      if (searchQuery.isNotEmpty) {
        sppgsToExport =
            sppgsToExport
                .where(
                  (sppg) => sppg.nama.toLowerCase().contains(
                    searchQuery.toLowerCase(),
                  ),
                )
                .toList();
      }

      if (sppgsToExport.isEmpty) {
        if (mounted) {
          Navigator.of(context).pop(); // Close progress dialog
        }
        _showErrorMessage('Tidak ada data SPPG untuk diekspor');
        return;
      }

      // Generate filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename = 'sppg_export_$timestamp';

      // Use export service
      final exportService = ExportServiceImpl();
      final result = await exportService.exportSppgs(
        sppgs: sppgsToExport,
        filename: filename,
        format: ExportFormat.csv,
        onProgress: (progress) {
          // Could emit progress updates here if needed
        },
      );

      if (mounted) {
        Navigator.of(context).pop(); // Close progress dialog
      }

      if (result.success) {
        if (mounted) {
          _showExportSuccessDialog(
            context,
            'CSV',
            result.filePath!,
            result.recordCount!,
          );
        }
      } else {
        if (mounted) {
          _showExportErrorDialog(context, 'CSV', result.errorMessage!);
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close progress dialog
      }
      _showErrorMessage('Gagal mengekspor data ke CSV: ${e.toString()}');
    }
  }

  /// Export SPPG data to Excel
  Future<void> _exportToExcel() async {
    try {
      _showExportProgressDialog(context, 'Excel');

      // Get filtered SPPGs based on current filters
      List<Sppg> sppgsToExport = _sppgs;

      // Apply current filters
      if (_selectedStatusFilter != null) {
        sppgsToExport =
            sppgsToExport
                .where((sppg) => sppg.status == _selectedStatusFilter)
                .toList();
      }

      if (_selectedTypeFilter != null) {
        sppgsToExport =
            sppgsToExport
                .where((sppg) => sppg.type == _selectedTypeFilter)
                .toList();
      }

      final searchQuery = _searchController.text.trim();
      if (searchQuery.isNotEmpty) {
        sppgsToExport =
            sppgsToExport
                .where(
                  (sppg) => sppg.nama.toLowerCase().contains(
                    searchQuery.toLowerCase(),
                  ),
                )
                .toList();
      }

      if (sppgsToExport.isEmpty) {
        if (mounted) {
          Navigator.of(context).pop(); // Close progress dialog
        }
        _showErrorMessage('Tidak ada data SPPG untuk diekspor');
        return;
      }

      // Generate filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename = 'sppg_export_$timestamp';

      // Use export service
      final exportService = ExportServiceImpl();
      final result = await exportService.exportSppgs(
        sppgs: sppgsToExport,
        filename: filename,
        format: ExportFormat.excel,
        onProgress: (progress) {
          // Could emit progress updates here if needed
        },
      );

      if (mounted) {
        Navigator.of(context).pop(); // Close progress dialog
      }

      if (result.success) {
        if (mounted) {
          _showExportSuccessDialog(
            context,
            'Excel',
            result.filePath!,
            result.recordCount!,
          );
        }
      } else {
        if (mounted) {
          _showExportErrorDialog(context, 'Excel', result.errorMessage!);
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close progress dialog
      }
      _showErrorMessage('Gagal mengekspor data ke Excel: ${e.toString()}');
    }
  }

  /// Show export progress dialog
  void _showExportProgressDialog(BuildContext context, String format) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => ContentDialog(
            title: Text('Mengekspor Data $format'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ProgressRing(),
                SizedBox(height: 16),
                Text('Sedang memproses data SPPG untuk ekspor...'),
                SizedBox(height: 8),
                Text(
                  'Mohon tunggu, proses ini mungkin memakan waktu beberapa saat.',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
    );
  }

  /// Show export success dialog
  void _showExportSuccessDialog(
    BuildContext context,
    String format,
    String filePath,
    int recordCount,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Export Berhasil'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      format == 'CSV'
                          ? FluentIcons.page_list
                          : FluentIcons.excel_document,
                      color: AppColors.successGreen,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Data SPPG telah berhasil diekspor ke $format.',
                        style: AppTypography.bodyLarge,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: FluentTheme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Lokasi File:',
                        style: AppTypography.caption.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      SelectableText(
                        filePath,
                        style: AppTypography.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Jumlah Data:',
                        style: AppTypography.caption.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$recordCount data SPPG',
                        style: AppTypography.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                const Text(
                  'Anda dapat membuka file ini dengan aplikasi spreadsheet seperti Microsoft Excel atau Google Sheets.',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            actions: [
              FilledButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  /// Show export error dialog
  void _showExportErrorDialog(
    BuildContext context,
    String format,
    String message,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Export Gagal'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(FluentIcons.error, color: Colors.red, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Gagal mengekspor data SPPG ke format $format',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Detail Error:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(message, style: const TextStyle(fontSize: 12)),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                const Text(
                  'Silakan coba lagi atau hubungi administrator jika masalah berlanjut.',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            actions: [
              Button(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Tutup'),
              ),
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Retry export based on format
                  if (format.toLowerCase() == 'csv') {
                    _exportToCSV();
                  } else {
                    _exportToExcel();
                  }
                },
                child: const Text('Coba Lagi'),
              ),
            ],
          ),
    );
  }

  /// Show error message
  void _showErrorMessage(String message) {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Error'),
            content: Text(message),
            actions: [
              FilledButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  Widget _buildHeader() {
    return PageHeader(
      title: const Text('Manajemen SPPG'),
      commandBar: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AppButtonFactory.primary(
            text: 'Tambah SPPG',
            icon: FluentIcons.add,
            onPressed: _showAddSppgDialog,
          ),
          const SizedBox(width: AppSpacing.sm),
          CommandBar(
            primaryItems: [
              CommandBarButton(
                icon: const Icon(FluentIcons.download),
                label: const Text('Ekspor'),
                onPressed: () {
                  // Show context menu for export options
                  showDialog(
                    context: context,
                    builder:
                        (context) => ContentDialog(
                          title: const Text('Pilih Format Export'),
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  Navigator.pop(context);
                                  _exportToCSV();
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  child: Row(
                                    children: [
                                      const Icon(FluentIcons.page_list),
                                      const SizedBox(width: 12),
                                      const Text('Export ke CSV'),
                                    ],
                                  ),
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  Navigator.pop(context);
                                  _exportToExcel();
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(12),
                                  child: Row(
                                    children: [
                                      const Icon(FluentIcons.excel_document),
                                      const SizedBox(width: 12),
                                      const Text('Export ke Excel'),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          actions: [
                            Button(
                              child: const Text('Batal'),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ],
                        ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        _buildSearchAndFilters(),
        const SizedBox(height: AppSpacing.md),
        Expanded(child: _buildSppgList()),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Column(
      children: [
        _buildSearchAndFilters(),
        const SizedBox(height: AppSpacing.md),
        Expanded(child: _buildSppgTable()),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Column(
      children: [
        _buildSearchAndFilters(),
        const SizedBox(height: AppSpacing.md),
        _buildStatsCards(),
        const SizedBox(height: AppSpacing.md),
        Expanded(child: _buildSppgTable()),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return AppCardFactory.elevated(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          children: [
            Row(
              children: [
                // Search
                Expanded(
                  flex: 3,
                  child: TextBox(
                    controller: _searchController,
                    placeholder: 'Cari SPPG berdasarkan nama...',
                    prefix: const Icon(FluentIcons.search),
                    onChanged: _onSearchChanged,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),

                // Status Filter
                Expanded(
                  child: ComboBox<SppgStatus>(
                    placeholder: const Text('Status'),
                    value: _selectedStatusFilter,
                    items:
                        SppgStatus.values
                            .map(
                              (status) => ComboBoxItem(
                                value: status,
                                child: Text(status.displayName),
                              ),
                            )
                            .toList(),
                    onChanged: _onStatusFilterChanged,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),

                // Type Filter
                Expanded(
                  child: ComboBox<SppgType>(
                    placeholder: const Text('Tipe'),
                    value: _selectedTypeFilter,
                    items:
                        SppgType.values
                            .map(
                              (type) => ComboBoxItem(
                                value: type,
                                child: Text(type.displayName),
                              ),
                            )
                            .toList(),
                    onChanged: _onTypeFilterChanged,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),

                // Clear Filters
                IconButton(
                  icon: const Icon(FluentIcons.clear),
                  onPressed: _clearFilters,
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            Row(
              children: [
                Button(
                  onPressed: _clearFilters,
                  child: const Text('Reset Filter'),
                ),
                const Spacer(),
                Button(
                  onPressed: _exportToCSV,
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(FluentIcons.download, size: 16),
                      SizedBox(width: 8),
                      Text('Export CSV'),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Button(
                  onPressed: _exportToExcel,
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(FluentIcons.excel_document, size: 16),
                      SizedBox(width: 8),
                      Text('Export Excel'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;

        // Only show stats cards on desktop
        if (!AppBreakpoints.isDesktop(screenWidth)) {
          return const SizedBox.shrink();
        }

        return StreamBuilder<Map<String, dynamic>>(
          stream: _getSppgStatsStream(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return _buildStatsGrid({}, true); // Show loading state
            }
            if (snapshot.hasError) {
              return _buildStatsError(snapshot.error.toString());
            }
            if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return _buildStatsError('Tidak ada data statistik.');
            }
            return _buildStatsGrid(snapshot.data!, false);
          },
        );
      },
    );
  }

  Stream<Map<String, dynamic>> _getSppgStatsStream() {
    return StatisticsService.instance.sppgAnalyticsStream.map((analytics) {
      final trends = StatisticsService.instance.getTrendIndicators();

      return {
        'total': {
          'value': analytics.total.toString(),
          'trend': trends['sppgTotal'],
        },
        'active': {
          'value': analytics.aktif.toString(),
          'trend': trends['sppgActive'],
        },
        'partner': {
          'value': analytics.mitra.toString(),
          'trend': trends['sppgPartner'],
        },
        'capacity': {
          'value': analytics.totalKapasitas.toString(),
          'trend': trends['sppgCapacity'],
        },
      };
    });
  }

  Widget _buildStatsGrid(Map<String, dynamic> stats, bool isLoading) {
    return Row(
      children: [
        Expanded(
          child: StatsCardWidget(
            title: 'Total SPPG',
            value: stats['total']?.toString() ?? '0',
            icon: FluentIcons.home,
            color: AppColors.primary,
            isLoading: isLoading,
            onTap: () => _onStatsCardTap('total'),
          ),
        ),
        Expanded(
          child: StatsCardWidget(
            title: 'SPPG Aktif',
            value: stats['active']?.toString() ?? '0',
            icon: FluentIcons.check_mark,
            color: AppColors.successGreen,
            isLoading: isLoading,
            onTap: () => _onStatsCardTap('active'),
          ),
        ),
        Expanded(
          child: StatsCardWidget(
            title: 'SPPG Mitra',
            value: stats['partner']?.toString() ?? '0',
            icon: FluentIcons.people,
            color: AppColors.infoBlue,
            isLoading: isLoading,
            onTap: () => _onStatsCardTap('partner'),
          ),
        ),
        Expanded(
          child: StatsCardWidget(
            title: 'Kapasitas Total',
            value: stats['capacity']?.toString() ?? '0',
            icon: FluentIcons.manufacturing,
            color: AppColors.warningOrange,
            isLoading: isLoading,
            onTap: () => _onStatsCardTap('capacity'),
          ),
        ),
      ],
    );
  }

  Widget _buildStatsError(String error) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: AppCardFactory.elevated(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Row(
            children: [
              const Icon(FluentIcons.error, color: AppColors.errorRed),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Text(
                  'Gagal memuat statistik: $error',
                  style: AppTypography.bodyMedium,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onStatsCardTap(String cardType) {
    // Handle stats card tap - could filter the table or show details
    switch (cardType) {
      case 'total':
        _clearFilters();
        break;
      case 'active':
        _onStatusFilterChanged(SppgStatus.aktif);
        break;
      case 'partner':
        _onTypeFilterChanged(SppgType.mitra);
        break;
      case 'capacity':
        // Could show capacity details or sort by capacity
        break;
    }
  }

  Widget _buildSppgList() {
    // List view of SPPG items
    return ListView.builder(
      itemCount: _sppgs.length,
      itemBuilder: (context, index) {
        final sppg = _sppgs[index];
        return _buildSppgCard(sppg);
      },
    );
  }

  Widget _buildSppgTable() {
    return SppgTableWidget(
      sppgs: _sppgs,
      onEdit: (sppgId) {
        final sppg = _sppgs.firstWhere((s) => s.id == sppgId);
        _showEditSppgDialog(sppg);
      },
      onDelete: (sppgId) {
        final sppg = _sppgs.firstWhere((s) => s.id == sppgId);
        _confirmDeleteSppg(sppg);
      },
      onStatusChange: (sppgId, newStatus) {
        context.read<SppgCubit>().updateSppgStatus(sppgId, newStatus);
        _loadData(); // Refresh data after status change
      },
      isLoading: false, // TODO: Connect to actual loading state from cubit
      bulkActions: [
        BulkAction(
          id: 'activate',
          label: 'Aktifkan Terpilih',
          icon: FluentIcons.play,
          onExecute: (selected) {
            context.read<SppgCubit>().bulkUpdateStatus(
              selected,
              SppgStatus.aktif,
            );
          },
        ),
        BulkAction(
          id: 'deactivate',
          label: 'Nonaktifkan Terpilih',
          icon: FluentIcons.pause,
          onExecute: (selected) {
            context.read<SppgCubit>().bulkUpdateStatus(
              selected,
              SppgStatus.nonAktif,
            );
          },
        ),
        BulkAction(
          id: 'delete',
          label: 'Hapus Terpilih',
          icon: FluentIcons.delete,
          onExecute: (selected) {
            // TODO: Implement bulk delete with confirmation
          },
        ),
      ],
    );
  }

  Widget _buildSppgCard(Sppg sppg) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      child: AppCardFactory.elevated(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(sppg.nama, style: AppTypography.bodyMedium),
                  StatusBadge(status: sppg.status),
                ],
              ),
              const SizedBox(height: AppSpacing.sm),
              Text(sppg.alamat, style: AppTypography.caption),
              const SizedBox(height: AppSpacing.md),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: const Icon(FluentIcons.edit),
                    onPressed: () => _showEditSppgDialog(sppg),
                  ),
                  IconButton(
                    icon: const Icon(
                      FluentIcons.delete,
                      color: AppColors.errorRed,
                    ),
                    onPressed: () => _confirmDeleteSppg(sppg),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldPage(
      header: _buildHeader(),
      content: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: ResponsiveLayout(
          mobile: _buildMobileLayout(),
          tablet: _buildTabletLayout(),
          desktop: _buildDesktopLayout(),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

/// Provider wrapper untuk SPPG Management Page
class SppgManagementPageProvider extends StatelessWidget {
  const SppgManagementPageProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) => SppgCubit(
            SppgSupabaseRepository(), // Ensure this implements SppgRepository
            core_connection.ConnectionManager.instance,
          )..loadSppgData(),
      child: const SppgManagementPage(),
    );
  }
}

class StatusBadge extends StatelessWidget {
  const StatusBadge({super.key, required this.status});

  final SppgStatus status;

  @override
  Widget build(BuildContext context) {
    Color color;
    String text;
    switch (status) {
      case SppgStatus.aktif:
        color = AppColors.successGreen;
        text = 'Aktif';
        break;
      case SppgStatus.nonAktif:
        color = AppColors.neutralGray200; // Replaced with a valid color
        text = 'Non-Aktif';
        break;
      case SppgStatus.suspend:
        color = AppColors.warningOrange;
        text = 'Suspend';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(text, style: AppTypography.caption.copyWith(color: color)),
    );
  }
}
