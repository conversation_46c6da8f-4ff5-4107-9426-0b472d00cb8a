import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logger/logger.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../../domain/entities/entities.dart';
import '../../domain/dashboard_summary.dart';

/// Cubit for managing dashboard data and state
class DashboardCubit extends Cubit<DashboardState> {
  final DashboardRepository _repository;
  final Logger _logger = Logger();

  DashboardCubit(this._repository) : super(DashboardInitial());

  /// Load dashboard data for a specific role
  Future<void> loadDashboard({String? roleId, String? sppgId}) async {
    _logger.i('Loading dashboard data for role: $roleId, sppgId: $sppgId');

    emit(DashboardLoading());

    try {
      final dashboardData = await _repository.getDashboardData(
        roleId ?? 'admin_yayasan',
        sppgId: sppgId,
        date: DateTime.now(),
      );

      emit(
        DashboardLoaded(
          configuration: dashboardData.configuration,
          kpiData: dashboardData.kpiData,
          pendingActions: dashboardData.pendingActions,
          sppgLocations: dashboardData.sppgLocations,
          activityEvents: dashboardData.activityEvents,
          performanceData: dashboardData.performanceData,
          lastUpdated: dashboardData.timestamp,
        ),
      );

      _logger.i('Dashboard data loaded successfully');
    } catch (e, stackTrace) {
      _logger.e('Failed to load dashboard data: $e', stackTrace: stackTrace);
      emit(
        DashboardError(
          message: 'Gagal memuat data dashboard: ${e.toString()}',
          isRetryable: true,
        ),
      );
    }
  }

  /// Refresh dashboard data
  Future<void> refreshDashboard({String? roleId, String? sppgId}) async {
    _logger.i('Refreshing dashboard data');

    final currentState = state;
    if (currentState is DashboardLoaded) {
      emit(DashboardRefreshing(currentState));

      try {
        await _repository.refreshDashboardData(roleId: roleId, sppgId: sppgId);

        final dashboardData = await _repository.getDashboardData(
          roleId ?? 'admin_yayasan',
          sppgId: sppgId,
          date: DateTime.now(),
        );

        emit(
          DashboardLoaded(
            configuration: dashboardData.configuration,
            kpiData: dashboardData.kpiData,
            pendingActions: dashboardData.pendingActions,
            sppgLocations: dashboardData.sppgLocations,
            activityEvents: dashboardData.activityEvents,
            performanceData: dashboardData.performanceData,
            lastUpdated: dashboardData.timestamp,
          ),
        );

        _logger.i('Dashboard data refreshed successfully');
      } catch (e, stackTrace) {
        _logger.e(
          'Failed to refresh dashboard data: $e',
          stackTrace: stackTrace,
        );
        emit(
          DashboardError(
            message: 'Gagal memperbarui data dashboard: ${e.toString()}',
            isRetryable: true,
          ),
        );
      }
    } else {
      // If not already loaded, perform a full load instead
      await loadDashboard(roleId: roleId, sppgId: sppgId);
    }
  }

  /// Load KPI data specifically
  Future<void> loadKpiData({String? roleId, String? sppgId}) async {
    _logger.i('Loading KPI data');

    final currentState = state;
    if (currentState is DashboardLoaded) {
      try {
        final kpiData = await _repository.kpiData.getKPIDataForRole(
          roleId ?? 'admin_yayasan',
          sppgId: sppgId,
        );

        emit(
          currentState.copyWith(kpiData: kpiData, lastUpdated: DateTime.now()),
        );

        _logger.i('KPI data loaded successfully');
      } catch (e, stackTrace) {
        _logger.e('Failed to load KPI data: $e', stackTrace: stackTrace);
      }
    }
  }

  /// Load pending actions
  Future<void> loadPendingActions({String? roleId, String? sppgId}) async {
    _logger.i('Loading pending actions');

    final currentState = state;
    if (currentState is DashboardLoaded) {
      try {
        final pendingActions = await _repository.pendingActions
            .getPendingActionsForRole(
              roleId ?? 'admin_yayasan',
              sppgId: sppgId,
            );

        emit(
          currentState.copyWith(
            pendingActions: pendingActions,
            lastUpdated: DateTime.now(),
          ),
        );

        _logger.i('Pending actions loaded successfully');
      } catch (e, stackTrace) {
        _logger.e('Failed to load pending actions: $e', stackTrace: stackTrace);
      }
    }
  }

  /// Load SPPG locations
  Future<void> loadSppgLocations({String? roleId}) async {
    _logger.i('Loading SPPG locations');

    final currentState = state;
    if (currentState is DashboardLoaded) {
      try {
        final sppgLocations = await _repository.sppgLocations
            .getSPPGLocationsForRole(roleId ?? 'admin_yayasan');

        emit(
          currentState.copyWith(
            sppgLocations: sppgLocations,
            lastUpdated: DateTime.now(),
          ),
        );

        _logger.i('SPPG locations loaded successfully');
      } catch (e, stackTrace) {
        _logger.e('Failed to load SPPG locations: $e', stackTrace: stackTrace);
      }
    }
  }

  /// Load activity events
  Future<void> loadActivityEvents({
    String? roleId,
    String? sppgId,
    int limit = 50,
  }) async {
    _logger.i('Loading activity events');

    final currentState = state;
    if (currentState is DashboardLoaded) {
      try {
        final activityEvents = await _repository.activityEvents
            .getRecentActivityEvents(
              roleId: roleId ?? 'admin_yayasan',
              sppgId: sppgId,
              limit: limit,
            );

        emit(
          currentState.copyWith(
            activityEvents: activityEvents,
            lastUpdated: DateTime.now(),
          ),
        );

        _logger.i('Activity events loaded successfully');
      } catch (e, stackTrace) {
        _logger.e('Failed to load activity events: $e', stackTrace: stackTrace);
      }
    }
  }

  /// Load performance data
  Future<void> loadPerformanceData({
    String? roleId,
    String? sppgId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    _logger.i('Loading performance data');

    final currentState = state;
    if (currentState is DashboardLoaded) {
      try {
        final performanceData =
            sppgId != null
                ? [
                  await _repository.performanceData.getSPPGPerformance(
                    sppgId,
                    period:
                        startDate != null && endDate != null
                            ? DateRange(
                              startDate: startDate,
                              endDate: endDate,
                              label: 'Custom Range',
                            )
                            : null,
                  ),
                ].whereType<PerformanceData>().toList()
                : await _repository.performanceData.getAllSPPGPerformance(
                  period:
                      startDate != null && endDate != null
                          ? DateRange(
                            startDate: startDate,
                            endDate: endDate,
                            label: 'Custom Range',
                          )
                          : null,
                );

        emit(
          currentState.copyWith(
            performanceData: performanceData,
            lastUpdated: DateTime.now(),
          ),
        );

        _logger.i('Performance data loaded successfully');
      } catch (e, stackTrace) {
        _logger.e(
          'Failed to load performance data: $e',
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// Get legacy dashboard summary (for backward compatibility)
  Future<DashboardSummary> getDashboardSummary(String role) async {
    return await _repository.getDashboardSummary(role);
  }
}

/// Dashboard state class
class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

/// Initial dashboard state
class DashboardInitial extends DashboardState {}

/// Loading dashboard state
class DashboardLoading extends DashboardState {}

/// Dashboard loaded state
class DashboardLoaded extends DashboardState {
  final DashboardConfiguration configuration;
  final List<KPIData> kpiData;
  final List<PendingAction> pendingActions;
  final List<SPPGLocation> sppgLocations;
  final List<ActivityEvent> activityEvents;
  final List<PerformanceData> performanceData;
  final DateTime lastUpdated;
  final Map<String, ComponentLoadingState> componentStates;

  const DashboardLoaded({
    required this.configuration,
    required this.kpiData,
    required this.pendingActions,
    required this.sppgLocations,
    required this.activityEvents,
    required this.performanceData,
    required this.lastUpdated,
    this.componentStates = const {},
  });

  @override
  List<Object?> get props => [
    configuration,
    kpiData,
    pendingActions,
    sppgLocations,
    activityEvents,
    performanceData,
    lastUpdated,
    componentStates,
  ];

  /// Create a copy with updated data
  DashboardLoaded copyWith({
    DashboardConfiguration? configuration,
    List<KPIData>? kpiData,
    List<PendingAction>? pendingActions,
    List<SPPGLocation>? sppgLocations,
    List<ActivityEvent>? activityEvents,
    List<PerformanceData>? performanceData,
    DateTime? lastUpdated,
    Map<String, ComponentLoadingState>? componentStates,
  }) {
    return DashboardLoaded(
      configuration: configuration ?? this.configuration,
      kpiData: kpiData ?? this.kpiData,
      pendingActions: pendingActions ?? this.pendingActions,
      sppgLocations: sppgLocations ?? this.sppgLocations,
      activityEvents: activityEvents ?? this.activityEvents,
      performanceData: performanceData ?? this.performanceData,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      componentStates: componentStates ?? this.componentStates,
    );
  }

  /// Update component state
  DashboardLoaded updateComponentState(
    String componentId,
    ComponentLoadingState state,
  ) {
    final updatedStates = Map<String, ComponentLoadingState>.from(
      componentStates,
    );
    updatedStates[componentId] = state;
    return copyWith(componentStates: updatedStates);
  }

  /// Add new activity event to the beginning of the list
  DashboardLoaded addActivityEvent(ActivityEvent event) {
    final updatedEvents = [event, ...activityEvents];
    // Keep only the latest 50 events to prevent memory issues
    final limitedEvents = updatedEvents.take(50).toList();
    return copyWith(activityEvents: limitedEvents, lastUpdated: DateTime.now());
  }
}

/// Dashboard error state
class DashboardError extends DashboardState {
  final String message;
  final String? componentId;
  final bool isRetryable;

  const DashboardError({
    required this.message,
    this.componentId,
    this.isRetryable = true,
  });

  @override
  List<Object?> get props => [message, componentId, isRetryable];
}

/// Dashboard refreshing state
class DashboardRefreshing extends DashboardState {
  final DashboardLoaded previousState;

  const DashboardRefreshing(this.previousState);

  @override
  List<Object?> get props => [previousState];
}

/// Enum for individual component loading states
enum ComponentLoadingState { initial, loading, loaded, error, refreshing }
