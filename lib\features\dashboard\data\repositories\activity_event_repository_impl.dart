import 'package:logger/logger.dart';
import '../../domain/entities/entities.dart';
import '../../domain/repositories/activity_event_repository.dart';
import '../datasources/activity_events_remote_datasource.dart';
import '../datasources/activity_events_local_datasource.dart';
import '../models/activity_event_model.dart';

/// Implementation of activity event repository with caching and offline support
class ActivityEventRepositoryImpl implements ActivityEventRepository {
  final ActivityEventsRemoteDataSource _remoteDataSource;
  final ActivityEventsLocalDataSource _localDataSource;
  final Logger _logger = Logger();

  ActivityEventRepositoryImpl(this._remoteDataSource, this._localDataSource);

  Future<List<ActivityEvent>> getRecentActivitiesForRole(
    String roleId, {
    String? sppgId,
    List<ActivityType>? types,
    List<ActivitySeverity>? severities,
    int limit = 50,
  }) async {
    _logger.d('Getting recent activities for role: $roleId');

    final cacheKey = _generateCacheKey(
      'recent_$roleId',
      sppgId: sppgId,
      types: types,
      severities: severities,
      limit: limit,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedActivityEvents(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached recent activities for role: $roleId');
        return cachedData.map((model) => model as ActivityEvent).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getRecentActivitiesForRole(
        roleId,
        sppgId: sppgId,
        types: types,
        severities: severities,
        limit: limit,
      );

      // Cache the data
      await _localDataSource.cacheActivityEvents(cacheKey, remoteData);

      _logger.i('Recent activities retrieved and cached for role: $roleId');
      return remoteData.map((model) => model as ActivityEvent).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get recent activities for role: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedActivityEvents(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as ActivityEvent).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  Future<List<ActivityEvent>> getActivitiesByType(
    ActivityType type, {
    String? sppgId,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    _logger.d('Getting activities by type: $type');

    final cacheKey = _generateCacheKey(
      'type_${type.name}',
      sppgId: sppgId,
      startDate: startDate,
      endDate: endDate,
      limit: limit,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedActivityEvents(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached activities by type: $type');
        return cachedData.map((model) => model as ActivityEvent).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getActivitiesByType(
        type,
        sppgId: sppgId,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );

      // Cache the data
      await _localDataSource.cacheActivityEvents(cacheKey, remoteData);

      _logger.i('Activities by type retrieved and cached: $type');
      return remoteData.map((model) => model as ActivityEvent).toList();
    } catch (e, stackTrace) {
      _logger.e('Failed to get activities by type: $e', stackTrace: stackTrace);

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedActivityEvents(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as ActivityEvent).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  Future<List<ActivityEvent>> getActivitiesBySeverity(
    ActivitySeverity severity, {
    String? sppgId,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    _logger.d('Getting activities by severity: $severity');

    final cacheKey = _generateCacheKey(
      'severity_${severity.name}',
      sppgId: sppgId,
      startDate: startDate,
      endDate: endDate,
      limit: limit,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedActivityEvents(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached activities by severity: $severity');
        return cachedData.map((model) => model as ActivityEvent).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getActivitiesBySeverity(
        severity,
        sppgId: sppgId,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );

      // Cache the data
      await _localDataSource.cacheActivityEvents(cacheKey, remoteData);

      _logger.i('Activities by severity retrieved and cached: $severity');
      return remoteData.map((model) => model as ActivityEvent).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get activities by severity: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedActivityEvents(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as ActivityEvent).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  // This is an additional method not in the interface but used internally
  Future<List<ActivityEvent>> getHistoricalActivities({
    required DateTime startDate,
    required DateTime endDate,
    String? sppgId,
    List<ActivityType>? types,
    List<ActivitySeverity>? severities,
    int limit = 100,
  }) async {
    _logger.d('Getting historical activities from $startDate to $endDate');

    final cacheKey = _generateCacheKey(
      'historical',
      sppgId: sppgId,
      types: types,
      severities: severities,
      startDate: startDate,
      endDate: endDate,
      limit: limit,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedActivityEvents(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached historical activities');
        return cachedData.map((model) => model as ActivityEvent).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getHistoricalActivities(
        startDate: startDate,
        endDate: endDate,
        sppgId: sppgId,
        types: types,
        severities: severities,
        limit: limit,
      );

      // Cache the data
      await _localDataSource.cacheActivityEvents(cacheKey, remoteData);

      _logger.i('Historical activities retrieved and cached');
      return remoteData.map((model) => model as ActivityEvent).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get historical activities: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedActivityEvents(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as ActivityEvent).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  Future<ActivityEvent?> getActivityEventById(String eventId) async {
    _logger.d('Getting activity event by ID: $eventId');

    final cacheKey = 'event_$eventId';

    try {
      // Try to get from cache first
      final cachedEvent = await _localDataSource.getCachedActivityEvent(
        cacheKey,
      );
      if (cachedEvent != null) {
        _logger.d('Returning cached activity event: $eventId');
        return cachedEvent;
      }

      // Fetch from remote source
      final remoteEvent = await _remoteDataSource.getActivityEventById(eventId);

      if (remoteEvent != null) {
        // Cache the event
        await _localDataSource.cacheActivityEvent(cacheKey, remoteEvent);
        _logger.i('Activity event retrieved and cached: $eventId');
      }

      return remoteEvent;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get activity event by ID: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleEvent = await _localDataSource.getCachedActivityEvent(
          cacheKey,
          allowStale: true,
        );
        if (staleEvent != null) {
          _logger.w('Returning stale cached event due to error');
          return staleEvent;
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached event: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<String> createActivityEvent(ActivityEvent event) async {
    _logger.d('Creating new activity event: ${event.title}');

    try {
      final eventModel = ActivityEventModel(
        id: event.id,
        title: event.title,
        description: event.description,
        sppgName: event.sppgName,
        type: event.type,
        timestamp: event.timestamp,
        severity: event.severity,
        userId: event.userId,
        userName: event.userName,
        data: event.data,
        isAcknowledged: event.isAcknowledged,
        relatedEntityId: event.relatedEntityId,
        tags: event.tags,
      );

      // Create in remote source
      final eventId = await _remoteDataSource.createActivityEvent(eventModel);

      // Clear related caches to force refresh
      await _localDataSource.clearAllActivityEventListCaches();

      _logger.i('Activity event created successfully: $eventId');
      return eventId;
    } catch (e, stackTrace) {
      _logger.e('Failed to create activity event: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> updateActivityEvent(
    String eventId, {
    bool? isAcknowledged,
    Map<String, dynamic>? data,
  }) async {
    _logger.d('Updating activity event: $eventId');

    try {
      // Update remote source
      await _remoteDataSource.updateActivityEvent(
        eventId,
        isAcknowledged: isAcknowledged,
        data: data,
      );

      // Clear related caches
      await _localDataSource.clearActivityEventCache(eventId);
      await _localDataSource.clearAllActivityEventListCaches();

      _logger.i('Activity event updated successfully: $eventId');
    } catch (e, stackTrace) {
      _logger.e('Failed to update activity event: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Stream<ActivityEvent> watchActivityEvents({
    String? sppgId,
    String? roleId,
    List<ActivityType>? types,
  }) {
    _logger.d('Starting activity events stream');

    return _remoteDataSource
        .watchNewActivityEvents(sppgId: sppgId, types: types)
        .map((model) => model as ActivityEvent);
  }

  // This is an additional method not in the interface but used internally
  Stream<List<ActivityEvent>> watchAllActivityEvents({
    String? sppgId,
    List<ActivityType>? types,
    List<ActivitySeverity>? severities,
    int limit = 50,
  }) {
    _logger.d('Starting all activity events stream');

    return _remoteDataSource
        .watchActivityEvents(
          sppgId: sppgId,
          types: types,
          severities: severities,
          limit: limit,
        )
        .map(
          (events) => events.map((model) => model as ActivityEvent).toList(),
        );
  }

  @override
  Future<List<ActivityEvent>> getRecentActivityEvents({
    String? sppgId,
    String? roleId,
    List<ActivityType>? types,
    List<ActivitySeverity>? severities,
    int limit = 50,
    DateTime? since,
  }) async {
    _logger.d('Getting recent activity events');

    // If roleId is provided, use the existing method
    if (roleId != null) {
      return getRecentActivitiesForRole(
        roleId,
        sppgId: sppgId,
        types: types,
        severities: severities,
        limit: limit,
      );
    }

    final cacheKey = _generateCacheKey(
      'recent',
      sppgId: sppgId,
      types: types,
      severities: severities,
      startDate: since,
      limit: limit,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedActivityEvents(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached recent activity events');
        return cachedData.map((model) => model as ActivityEvent).toList();
      }

      // Fetch from remote source - use existing methods based on available parameters
      List<ActivityEventModel> remoteData;

      if (types != null && types.length == 1) {
        // If only one type is specified, use the type-specific method
        remoteData = await _remoteDataSource.getActivitiesByType(
          types.first,
          sppgId: sppgId,
          startDate: since,
          limit: limit,
        );
      } else if (severities != null && severities.length == 1) {
        // If only one severity is specified, use the severity-specific method
        remoteData = await _remoteDataSource.getActivitiesBySeverity(
          severities.first,
          sppgId: sppgId,
          startDate: since,
          limit: limit,
        );
      } else {
        // Otherwise use historical activities with appropriate date range
        final endDate = DateTime.now();
        final startDate = since ?? endDate.subtract(const Duration(days: 7));

        remoteData = await _remoteDataSource.getHistoricalActivities(
          startDate: startDate,
          endDate: endDate,
          sppgId: sppgId,
          types: types,
          severities: severities,
          limit: limit,
        );
      }

      // Cache the data
      await _localDataSource.cacheActivityEvents(cacheKey, remoteData);

      _logger.i('Recent activity events retrieved and cached');
      return remoteData.map((model) => model as ActivityEvent).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get recent activity events: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedActivityEvents(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as ActivityEvent).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<List<ActivityEvent>> getActivityEventsForSPPG(
    String sppgId, {
    DateTime? startDate,
    DateTime? endDate,
    List<ActivityType>? types,
    int? limit,
  }) async {
    _logger.d('Getting activity events for SPPG: $sppgId');

    final cacheKey = _generateCacheKey(
      'sppg_$sppgId',
      types: types,
      startDate: startDate,
      endDate: endDate,
      limit: limit,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedActivityEvents(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached activity events for SPPG: $sppgId');
        return cachedData.map((model) => model as ActivityEvent).toList();
      }

      // Fetch from remote source
      final now = DateTime.now();
      final effectiveEndDate = endDate ?? now;
      final effectiveStartDate =
          startDate ?? effectiveEndDate.subtract(const Duration(days: 30));
      final effectiveLimit = limit ?? 100;

      final remoteData = await _remoteDataSource.getHistoricalActivities(
        startDate: effectiveStartDate,
        endDate: effectiveEndDate,
        sppgId: sppgId,
        types: types,
        limit: effectiveLimit,
      );

      // Cache the data
      await _localDataSource.cacheActivityEvents(cacheKey, remoteData);

      _logger.i('Activity events for SPPG retrieved and cached: $sppgId');
      return remoteData.map((model) => model as ActivityEvent).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get activity events for SPPG: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedActivityEvents(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as ActivityEvent).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<List<ActivityEvent>> getActivityEventsByType(
    ActivityType type, {
    String? sppgId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    _logger.d('Getting activity events by type: $type');

    final effectiveLimit = limit ?? 100;

    return getActivitiesByType(
      type,
      sppgId: sppgId,
      startDate: startDate,
      endDate: endDate,
      limit: effectiveLimit,
    );
  }

  @override
  Future<void> acknowledgeActivityEvent(String eventId) async {
    _logger.d('Acknowledging activity event: $eventId');

    try {
      // Update remote source
      await _remoteDataSource.updateActivityEvent(
        eventId,
        isAcknowledged: true,
      );

      // Clear related caches
      await _localDataSource.clearActivityEventCache(eventId);
      await _localDataSource.clearAllActivityEventListCaches();

      _logger.i('Activity event acknowledged successfully: $eventId');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to acknowledge activity event: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<Map<String, int>> getActivityStatistics(
    DateRange dateRange, {
    String? sppgId,
    String? roleId,
  }) async {
    _logger.d('Getting activity statistics for date range: ${dateRange.label}');

    final cacheKey = _generateCacheKey(
      'stats',
      sppgId: sppgId,
      roleId: roleId,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedActivityEvents(
        cacheKey,
      );

      if (cachedData != null) {
        _logger.d('Calculating statistics from cached data');
        return _calculateStatistics(cachedData);
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getHistoricalActivities(
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        sppgId: sppgId,
        limit: 1000, // Get a large sample for statistics
      );

      // Cache the data
      await _localDataSource.cacheActivityEvents(cacheKey, remoteData);

      _logger.i('Activity statistics calculated from remote data');
      return _calculateStatistics(remoteData);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get activity statistics: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedActivityEvents(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Calculating statistics from stale cached data');
          return _calculateStatistics(staleData);
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      // Return empty statistics as fallback
      return {};
    }
  }

  @override
  Future<List<ActivityEvent>> getCriticalActivityEvents({
    String? sppgId,
    String? roleId,
  }) async {
    _logger.d('Getting critical activity events');

    final cacheKey = _generateCacheKey(
      'critical',
      sppgId: sppgId,
      roleId: roleId,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedActivityEvents(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached critical activity events');
        return cachedData.map((model) => model as ActivityEvent).toList();
      }

      // Fetch critical events (error and critical severity)
      final errorEvents = await _remoteDataSource.getActivitiesBySeverity(
        ActivitySeverity.error,
        sppgId: sppgId,
        limit: 50,
      );

      final criticalEvents = await _remoteDataSource.getActivitiesBySeverity(
        ActivitySeverity.critical,
        sppgId: sppgId,
        limit: 50,
      );

      // Combine and sort by timestamp (newest first)
      final combinedEvents = [...errorEvents, ...criticalEvents];
      combinedEvents.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // Limit to 100 events
      final limitedEvents = combinedEvents.take(100).toList();

      // Cache the data
      await _localDataSource.cacheActivityEvents(cacheKey, limitedEvents);

      _logger.i('Critical activity events retrieved and cached');
      return limitedEvents.map((model) => model as ActivityEvent).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get critical activity events: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedActivityEvents(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as ActivityEvent).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  /// Calculate statistics from activity events
  Map<String, int> _calculateStatistics(List<ActivityEventModel> events) {
    final stats = <String, int>{};

    // Count by type
    for (final event in events) {
      final typeKey = 'type_${event.type.name}';
      stats[typeKey] = (stats[typeKey] ?? 0) + 1;
    }

    // Count by severity
    for (final event in events) {
      final severityKey = 'severity_${event.severity.name}';
      stats[severityKey] = (stats[severityKey] ?? 0) + 1;
    }

    // Count by day of week
    for (final event in events) {
      final dayKey = 'day_${event.timestamp.weekday}';
      stats[dayKey] = (stats[dayKey] ?? 0) + 1;
    }

    // Count by hour of day
    for (final event in events) {
      final hourKey = 'hour_${event.timestamp.hour}';
      stats[hourKey] = (stats[hourKey] ?? 0) + 1;
    }

    // Count total
    stats['total'] = events.length;

    // Count acknowledged vs unacknowledged
    stats['acknowledged'] = events.where((e) => e.isAcknowledged).length;
    stats['unacknowledged'] = events.where((e) => !e.isAcknowledged).length;

    return stats;
  }

  /// Generate cache key for activity events
  String _generateCacheKey(
    String prefix, {
    String? sppgId,
    String? roleId,
    List<ActivityType>? types,
    List<ActivitySeverity>? severities,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) {
    final parts = [prefix];

    if (sppgId != null) {
      parts.add('sppg_$sppgId');
    }

    if (roleId != null) {
      parts.add('role_$roleId');
    }

    if (types != null && types.isNotEmpty) {
      final typeNames = types.map((t) => t.name).join(',');
      parts.add('types_$typeNames');
    }

    if (severities != null && severities.isNotEmpty) {
      final severityNames = severities.map((s) => s.name).join(',');
      parts.add('severities_$severityNames');
    }

    if (startDate != null) {
      parts.add('start_${startDate.toIso8601String().split('T')[0]}');
    }

    if (endDate != null) {
      parts.add('end_${endDate.toIso8601String().split('T')[0]}');
    }

    if (limit != null) {
      parts.add('limit_$limit');
    }

    return parts.join('_');
  }
}
