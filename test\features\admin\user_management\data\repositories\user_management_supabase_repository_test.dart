import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:aplikasi_sppg/features/admin/user_management/data/repositories/user_management_supabase_repository.dart';
import 'package:aplikasi_sppg/features/admin/user_management/domain/models/user_management.dart';

// Generate mocks
@GenerateMocks([SupabaseClient, SupabaseQueryBuilder, PostgrestFilterBuilder])
// ignore: must_be_immutable
class MockSupabaseClient extends Mock implements SupabaseClient {}
// ignore: must_be_immutable
class MockSupabaseQueryBuilder extends Mock implements SupabaseQueryBuilder {}
// ignore: must_be_immutable
class MockPostgrestFilterBuilder extends Mock implements PostgrestFilterBuilder {}

void main() {
  group('UserManagementSupabaseRepository Tests', () {
    late UserManagementSupabaseRepository repository;
    // late MockSupabaseClient mockClient; // TODO: Implement mock tests
    // late MockSupabaseQueryBuilder mockQueryBuilder; // TODO: Implement mock tests
    // late MockPostgrestFilterBuilder mockFilterBuilder; // TODO: Implement mock tests
    late UserManagement testUser;
    // late Map<String, dynamic> testUserJson; // TODO: Implement JSON tests
    late Map<String, dynamic> testDatabaseJson;

    setUp(() {
      // mockClient = MockSupabaseClient(); // TODO: Implement mock setup
      // mockQueryBuilder = MockSupabaseQueryBuilder(); // TODO: Implement mock setup
      // mockFilterBuilder = MockPostgrestFilterBuilder(); // TODO: Implement mock setup
      repository = UserManagementSupabaseRepository();
      
      testUser = UserManagement(
        id: 'user-123',
        nama: 'Test User',
        email: '<EMAIL>',
        telepon: '+6281234567890',
        role: UserRole.kepalaDapur,
        status: UserStatus.active,
        sppgId: 'sppg-456',
        sppgName: 'SPPG Test',
        createdAt: DateTime(2024, 1, 1, 10, 30),
        lastLoginAt: DateTime(2024, 1, 15, 14, 45),
        alamat: 'Jl. Test No. 123',
        nip: '1234567890123456',
        permissions: {'manage_kitchen': true},
        notes: 'Test user notes',
      );

      // testUserJson = { // TODO: Implement JSON test data
      //   'id': 'user-123',
      //   'nama': 'Test User',
      //   'email': '<EMAIL>',
      //   'telepon': '+6281234567890',
      //   'role': 'kepalaDapur',
      //   'status': 'active',
      //   'sppgId': 'sppg-456',
      //   'sppgName': 'SPPG Test',
      //   'createdAt': '2024-01-01T10:30:00.000Z',
      //   'lastLoginAt': '2024-01-15T14:45:00.000Z',
      //   'profileImageUrl': null,
      //   'alamat': 'Jl. Test No. 123',
      //   'nip': '1234567890123456',
      //   'permissions': {'manage_kitchen': true},
      //   'notes': 'Test user notes',
      //   'suspendedUntil': null,
      // };

      testDatabaseJson = {
        'id': 'user-123',
        'nama': 'Test User',
        'email': '<EMAIL>',
        'telepon': '+6281234567890',
        'role': 'kepalaDapur',
        'status': 'active',
        'sppg_id': 'sppg-456',
        'sppg_name': 'SPPG Test',
        'created_at': '2024-01-01T10:30:00.000Z',
        'last_login_at': '2024-01-15T14:45:00.000Z',
        'profile_image_url': null,
        'alamat': 'Jl. Test No. 123',
        'nip': '1234567890123456',
        'permissions': {'manage_kitchen': true},
        'notes': 'Test user notes',
        'suspended_until': null,
      };
    });

    group('Repository Configuration', () {
      test('should have correct table name', () {
        expect(repository.tableName, 'user_profiles');
      });

      test('should get correct ID from model', () {
        final id = repository.getId(testUser);
        expect(id, 'user-123');
      });
    });

    group('JSON Conversion', () {
      test('fromJson should convert database JSON to UserManagement model', () {
        final user = repository.fromJson(testDatabaseJson);
        
        expect(user.id, 'user-123');
        expect(user.nama, 'Test User');
        expect(user.email, '<EMAIL>');
        expect(user.telepon, '+6281234567890');
        expect(user.role, UserRole.kepalaDapur);
        expect(user.status, UserStatus.active);
        expect(user.sppgId, 'sppg-456');
        expect(user.sppgName, 'SPPG Test');
        expect(user.alamat, 'Jl. Test No. 123');
        expect(user.nip, '1234567890123456');
        expect(user.permissions['manage_kitchen'], isTrue);
        expect(user.notes, 'Test user notes');
      });

      test('toCreateJson should convert model to database format', () {
        final json = repository.toCreateJson(testUser);
        
        expect(json['nama'], 'Test User');
        expect(json['email'], '<EMAIL>');
        expect(json['telepon'], '+6281234567890');
        expect(json['role'], 'kepalaDapur');
        expect(json['status'], 'active');
        expect(json['sppg_id'], 'sppg-456'); // Converted to database field name
        expect(json['alamat'], 'Jl. Test No. 123');
        expect(json['nip'], '1234567890123456');
        expect(json['permissions'], {'manage_kitchen': true});
        expect(json['notes'], 'Test user notes');
      });

      test('toUpdateJson should convert model to database format with ID', () {
        final json = repository.toUpdateJson(testUser);
        
        expect(json['id'], 'user-123');
        expect(json['nama'], 'Test User');
        expect(json['sppg_id'], 'sppg-456'); // Converted to database field name
        expect(json.containsKey('updated_at'), isTrue);
      });

      test('should handle null values in database JSON', () {
        final jsonWithNulls = {
          'id': 'user-123',
          'nama': 'Test User',
          'email': '<EMAIL>',
          'telepon': '+6281234567890',
          'role': 'adminYayasan',
          'status': 'active',
          'sppg_id': null,
          'sppg_name': null,
          'created_at': '2024-01-01T10:30:00.000Z',
          'last_login_at': null,
          'profile_image_url': null,
          'alamat': null,
          'nip': null,
          'permissions': null,
          'notes': null,
          'suspended_until': null,
        };

        final user = repository.fromJson(jsonWithNulls);
        
        expect(user.sppgId, isNull);
        expect(user.sppgName, isNull);
        expect(user.lastLoginAt, isNull);
        expect(user.alamat, isNull);
        expect(user.nip, isNull);
        expect(user.permissions, isEmpty); // Should default to empty map
        expect(user.notes, isNull);
        expect(user.suspendedUntil, isNull);
      });
    });

    group('Field Name Conversion', () {
      test('should convert model field names to database field names', () {
        // final modelData = { // TODO: Implement model conversion test
        //   'sppgId': 'sppg-123',
        //   'sppgName': 'SPPG Test',
        //   'createdAt': '2024-01-01T10:30:00.000Z',
        //   'lastLoginAt': '2024-01-15T14:45:00.000Z',
        //   'profileImageUrl': 'https://example.com/image.jpg',
        //   'suspendedUntil': '2024-12-31T23:59:59.000Z',
        //   'nama': 'Test User',
        //   'email': '<EMAIL>',
        // };

        // Test the conversion logic by creating a user and converting to create request
        final user = UserManagement(
          id: 'test-id',
          nama: 'Test User',
          email: '<EMAIL>',
          telepon: '+6281234567890',
          role: UserRole.adminYayasan,
          status: UserStatus.active,
          createdAt: DateTime.now(),
        );

        final createJson = repository.toCreateJson(user);
        
        // Check that regular fields are preserved
        expect(createJson['nama'], 'Test User');
        expect(createJson['email'], '<EMAIL>');
        
        // Check that special fields would be converted if present
        if (createJson.containsKey('sppg_id')) {
          expect(createJson.containsKey('sppgId'), isFalse);
        }
      });

      test('should convert database field names to model field names', () {
        final databaseData = {
          'id': 'user-123',
          'nama': 'Test User',
          'email': '<EMAIL>',
          'telepon': '+6281234567890',
          'role': 'adminYayasan',
          'status': 'active',
          'sppg_id': 'sppg-456',
          'sppg_name': 'SPPG Test',
          'created_at': '2024-01-01T10:30:00.000Z',
          'last_login_at': '2024-01-15T14:45:00.000Z',
          'profile_image_url': 'https://example.com/image.jpg',
          'suspended_until': '2024-12-31T23:59:59.000Z',
          'alamat': 'Jl. Test No. 123',
          'nip': '1234567890123456',
          'permissions': {'test': true},
          'notes': 'Test notes',
        };

        final user = repository.fromJson(databaseData);
        
        expect(user.sppgId, 'sppg-456');
        expect(user.sppgName, 'SPPG Test');
        expect(user.profileImageUrl, 'https://example.com/image.jpg');
        expect(user.suspendedUntil, isNotNull);
      });
    });

    group('Search Functionality', () {
      test('should apply search to correct fields', () {
        // Since we can't easily mock the Supabase query builder,
        // we'll test the search logic conceptually
        const searchTerm = 'test';
        
        // Test that search would match various fields
        final testData = [
          {'nama': 'Test User', 'email': '<EMAIL>'},
          {'nama': 'John Doe', 'email': '<EMAIL>'},
          {'nama': 'Jane Smith', 'telepon': '+6281234567890', 'sppg_name': 'SPPG Test'},
          {'nama': 'Bob Wilson', 'email': '<EMAIL>'},
        ];

        // Simulate search matching
        final matchingItems = testData.where((item) {
          final searchLower = searchTerm.toLowerCase();
          return item['nama']?.toString().toLowerCase().contains(searchLower) == true ||
                 item['email']?.toString().toLowerCase().contains(searchLower) == true ||
                 item['telepon']?.toString().toLowerCase().contains(searchLower) == true ||
                 item['sppg_name']?.toString().toLowerCase().contains(searchLower) == true;
        }).toList();

        expect(matchingItems.length, 3); // Should match first 3 items
      });

      test('should handle empty search query', () {
        const searchQuery = '';
        
        // Empty search should not filter anything
        expect(searchQuery.trim().isEmpty, isTrue);
      });

      test('should handle search with special characters', () {
        const searchQuery = '<EMAIL>';
        final searchTerm = searchQuery.toLowerCase().trim();
        
        expect(searchTerm, '<EMAIL>');
        expect(searchTerm.contains('@'), isTrue);
      });
    });

    group('Data Validation', () {
      test('should validate user data structure', () {
        // Test that the user model has all required fields
        expect(testUser.id.isNotEmpty, isTrue);
        expect(testUser.nama.isNotEmpty, isTrue);
        expect(testUser.email.isNotEmpty, isTrue);
        expect(testUser.telepon.isNotEmpty, isTrue);
        expect(testUser.role, isNotNull);
        expect(testUser.status, isNotNull);
        expect(testUser.createdAt, isNotNull);
      });

      test('should validate JSON structure for database operations', () {
        final createJson = repository.toCreateJson(testUser);
        
        // Check required fields are present
        expect(createJson['nama'], isNotNull);
        expect(createJson['email'], isNotNull);
        expect(createJson['telepon'], isNotNull);
        expect(createJson['role'], isNotNull);
        expect(createJson['status'], isNotNull);
        
        // Check that database field names are used
        if (testUser.sppgId != null) {
          expect(createJson.containsKey('sppg_id'), isTrue);
          expect(createJson.containsKey('sppgId'), isFalse);
        }
      });

      test('should handle different user roles correctly', () {
        final roles = [
          UserRole.adminYayasan,
          UserRole.perwakilanYayasan,
          UserRole.kepalaDapur,
          UserRole.ahliGizi,
          UserRole.akuntan,
          UserRole.pengawasPemeliharaan,
        ];

        for (final role in roles) {
          final user = testUser.copyWith(role: role);
          final json = repository.toCreateJson(user);
          
          expect(json['role'], role.name);
          
          final recreated = repository.fromJson({
            ...testDatabaseJson,
            'role': role.name,
          });
          
          expect(recreated.role, role);
        }
      });

      test('should handle different user statuses correctly', () {
        final statuses = [
          UserStatus.active,
          UserStatus.inactive,
          UserStatus.suspended,
          UserStatus.pending,
        ];

        for (final status in statuses) {
          final user = testUser.copyWith(status: status);
          final json = repository.toCreateJson(user);
          
          expect(json['status'], status.name);
          
          final recreated = repository.fromJson({
            ...testDatabaseJson,
            'status': status.name,
          });
          
          expect(recreated.status, status);
        }
      });
    });

    group('Error Handling', () {
      test('should handle invalid JSON gracefully', () {
        final invalidJson = {
          'id': 'user-123',
          'nama': 'Test User',
          // Missing required fields
        };

        expect(() => repository.fromJson(invalidJson), throwsA(isA<Exception>()));
      });

      test('should handle invalid role values', () {
        final jsonWithInvalidRole = {
          ...testDatabaseJson,
          'role': 'invalidRole',
        };

        expect(() => repository.fromJson(jsonWithInvalidRole), throwsA(isA<Exception>()));
      });

      test('should handle invalid status values', () {
        final jsonWithInvalidStatus = {
          ...testDatabaseJson,
          'status': 'invalidStatus',
        };

        expect(() => repository.fromJson(jsonWithInvalidStatus), throwsA(isA<Exception>()));
      });

      test('should handle malformed date strings', () {
        final jsonWithInvalidDate = {
          ...testDatabaseJson,
          'created_at': 'invalid-date',
        };

        expect(() => repository.fromJson(jsonWithInvalidDate), throwsA(isA<Exception>()));
      });
    });

    group('Edge Cases', () {
      test('should handle user with minimal data', () {
        final minimalUser = UserManagement(
          id: 'minimal-user',
          nama: 'Minimal User',
          email: '<EMAIL>',
          telepon: '+6281111111111',
          role: UserRole.adminYayasan,
          status: UserStatus.pending,
          createdAt: DateTime.now(),
        );

        final json = repository.toCreateJson(minimalUser);
        expect(json['nama'], 'Minimal User');
        expect(json['email'], '<EMAIL>');
        expect(json['role'], 'adminYayasan');
        expect(json['status'], 'pending');
        
        // Optional fields should not be present or be null
        expect(json['sppg_id'], isNull);
        expect(json['alamat'], isNull);
        expect(json['nip'], isNull);
      });

      test('should handle user with maximum data', () {
        final maximalUser = UserManagement(
          id: 'maximal-user',
          nama: 'Maximal User',
          email: '<EMAIL>',
          telepon: '+6281111111111',
          role: UserRole.kepalaDapur,
          status: UserStatus.active,
          sppgId: 'sppg-123',
          sppgName: 'SPPG Test',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          profileImageUrl: 'https://example.com/image.jpg',
          alamat: 'Jl. Maximal No. 123, Jakarta',
          nip: '1234567890123456',
          permissions: {
            'manage_kitchen': true,
            'view_reports': true,
            'manage_inventory': false,
          },
          notes: 'This is a test user with all possible fields filled',
          suspendedUntil: DateTime.now().add(Duration(days: 30)),
        );

        final json = repository.toCreateJson(maximalUser);
        
        // All fields should be present
        expect(json['nama'], isNotNull);
        expect(json['email'], isNotNull);
        expect(json['sppg_id'], isNotNull);
        expect(json['alamat'], isNotNull);
        expect(json['nip'], isNotNull);
        expect(json['permissions'], isA<Map>());
        expect(json['notes'], isNotNull);
        expect(json['suspended_until'], isNotNull);
      });

      test('should handle very long text fields', () {
        final longText = 'A' * 1000;
        final userWithLongText = testUser.copyWith(
          nama: longText,
          alamat: longText,
          notes: longText,
        );

        final json = repository.toCreateJson(userWithLongText);
        
        expect(json['nama'].length, 1000);
        expect(json['alamat'].length, 1000);
        expect(json['notes'].length, 1000);
      });

      test('should handle special characters in text fields', () {
        final userWithSpecialChars = testUser.copyWith(
          nama: 'User with émojis 🎉 and spëcial chars',
          alamat: 'Jl. Spëcial Chars & Symbols #123, Jakarta',
          notes: 'Notes with "quotes", \'apostrophes\', and newlines\nLine 2',
        );

        final json = repository.toCreateJson(userWithSpecialChars);
        
        expect(json['nama'], contains('émojis'));
        expect(json['nama'], contains('🎉'));
        expect(json['alamat'], contains('&'));
        expect(json['alamat'], contains('#'));
        expect(json['notes'], contains('"'));
        expect(json['notes'], contains('\''));
        expect(json['notes'], contains('\n'));
      });
    });
  });
}