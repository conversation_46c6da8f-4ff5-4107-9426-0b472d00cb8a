import 'package:equatable/equatable.dart';

/// Real-time activity event for dashboard feed
class ActivityEvent extends Equatable {
  /// Unique identifier for the event
  final String id;

  /// Event title
  final String title;

  /// Detailed description
  final String description;

  /// ID of the SPPG where event occurred
  final String? sppgId;

  /// Name of the SPPG where event occurred
  final String sppgName;

  /// Type of activity
  final ActivityType type;

  /// Event timestamp
  final DateTime timestamp;

  /// Severity level
  final ActivitySeverity severity;

  /// User who triggered the event (if applicable)
  final String? userId;

  /// User name who triggered the event
  final String? userName;

  /// Additional structured data
  final Map<String, dynamic>? data;

  /// Whether this event has been acknowledged
  final bool isAcknowledged;

  /// Related entity ID (e.g., order ID, report ID)
  final String? relatedEntityId;

  /// Tags for categorization
  final List<String>? tags;

  /// Roles that can see this activity event
  final List<String> visibleToRoles;

  const ActivityEvent({
    required this.id,
    required this.title,
    required this.description,
    this.sppgId,
    required this.sppgName,
    required this.type,
    required this.timestamp,
    required this.severity,
    this.userId,
    this.userName,
    this.data,
    this.isAcknowledged = false,
    this.relatedEntityId,
    this.tags,
    this.visibleToRoles = const ['admin_yayasan'],
  });

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    sppgId,
    sppgName,
    type,
    timestamp,
    severity,
    userId,
    userName,
    data,
    isAcknowledged,
    relatedEntityId,
    tags,
    visibleToRoles,
  ];
}

/// Type of activity that occurred
enum ActivityType {
  /// Order-related activities
  orderCreated,
  orderUpdated,
  orderCompleted,
  orderCancelled,

  /// Production activities
  productionStarted,
  productionCompleted,
  productionIssue,

  /// Quality control activities
  qcCheckStarted,
  qcCheckPassed,
  qcCheckFailed,
  qcIssueReported,

  /// Inventory activities
  inventoryUpdated,
  stockLevelLow,
  stockLevelCritical,
  inventoryAdjustment,

  /// Delivery activities
  deliveryScheduled,
  deliveryStarted,
  deliveryCompleted,
  deliveryDelayed,
  deliveryFailed,

  /// System activities
  userLogin,
  userLogout,
  systemMaintenance,
  configurationChanged,

  /// Financial activities
  paymentReceived,
  expenseRecorded,
  budgetAlert,

  /// Maintenance activities
  maintenanceScheduled,
  maintenanceCompleted,
  equipmentFailure,

  /// General activities
  reportGenerated,
  approvalRequested,
  approvalGranted,
  approvalDenied,
  notification,
  alert,
}

/// Severity level for activities
enum ActivitySeverity {
  /// Informational - general activities
  info,

  /// Warning - potential issues
  warning,

  /// Error - problems that need attention
  error,

  /// Critical - urgent issues requiring immediate action
  critical,

  /// Success - positive outcomes
  success,
}

/// Extension methods for ActivityType
extension ActivityTypeExtension on ActivityType {
  /// Human-readable name in Indonesian
  String get displayName {
    switch (this) {
      case ActivityType.orderCreated:
        return 'Pesanan Dibuat';
      case ActivityType.orderUpdated:
        return 'Pesanan Diperbarui';
      case ActivityType.orderCompleted:
        return 'Pesanan Selesai';
      case ActivityType.orderCancelled:
        return 'Pesanan Dibatalkan';
      case ActivityType.productionStarted:
        return 'Produksi Dimulai';
      case ActivityType.productionCompleted:
        return 'Produksi Selesai';
      case ActivityType.productionIssue:
        return 'Masalah Produksi';
      case ActivityType.qcCheckStarted:
        return 'QC Dimulai';
      case ActivityType.qcCheckPassed:
        return 'QC Lulus';
      case ActivityType.qcCheckFailed:
        return 'QC Gagal';
      case ActivityType.qcIssueReported:
        return 'Masalah QC Dilaporkan';
      case ActivityType.inventoryUpdated:
        return 'Inventori Diperbarui';
      case ActivityType.stockLevelLow:
        return 'Stok Menipis';
      case ActivityType.stockLevelCritical:
        return 'Stok Kritis';
      case ActivityType.inventoryAdjustment:
        return 'Penyesuaian Inventori';
      case ActivityType.deliveryScheduled:
        return 'Pengiriman Dijadwalkan';
      case ActivityType.deliveryStarted:
        return 'Pengiriman Dimulai';
      case ActivityType.deliveryCompleted:
        return 'Pengiriman Selesai';
      case ActivityType.deliveryDelayed:
        return 'Pengiriman Tertunda';
      case ActivityType.deliveryFailed:
        return 'Pengiriman Gagal';
      case ActivityType.userLogin:
        return 'Pengguna Masuk';
      case ActivityType.userLogout:
        return 'Pengguna Keluar';
      case ActivityType.systemMaintenance:
        return 'Pemeliharaan Sistem';
      case ActivityType.configurationChanged:
        return 'Konfigurasi Diubah';
      case ActivityType.paymentReceived:
        return 'Pembayaran Diterima';
      case ActivityType.expenseRecorded:
        return 'Pengeluaran Dicatat';
      case ActivityType.budgetAlert:
        return 'Peringatan Anggaran';
      case ActivityType.maintenanceScheduled:
        return 'Pemeliharaan Dijadwalkan';
      case ActivityType.maintenanceCompleted:
        return 'Pemeliharaan Selesai';
      case ActivityType.equipmentFailure:
        return 'Kerusakan Peralatan';
      case ActivityType.reportGenerated:
        return 'Laporan Dibuat';
      case ActivityType.approvalRequested:
        return 'Persetujuan Diminta';
      case ActivityType.approvalGranted:
        return 'Persetujuan Diberikan';
      case ActivityType.approvalDenied:
        return 'Persetujuan Ditolak';
      case ActivityType.notification:
        return 'Notifikasi';
      case ActivityType.alert:
        return 'Peringatan';
    }
  }

  /// Category grouping for filtering
  String get category {
    switch (this) {
      case ActivityType.orderCreated:
      case ActivityType.orderUpdated:
      case ActivityType.orderCompleted:
      case ActivityType.orderCancelled:
        return 'Pesanan';
      case ActivityType.productionStarted:
      case ActivityType.productionCompleted:
      case ActivityType.productionIssue:
        return 'Produksi';
      case ActivityType.qcCheckStarted:
      case ActivityType.qcCheckPassed:
      case ActivityType.qcCheckFailed:
      case ActivityType.qcIssueReported:
        return 'Quality Control';
      case ActivityType.inventoryUpdated:
      case ActivityType.stockLevelLow:
      case ActivityType.stockLevelCritical:
      case ActivityType.inventoryAdjustment:
        return 'Inventori';
      case ActivityType.deliveryScheduled:
      case ActivityType.deliveryStarted:
      case ActivityType.deliveryCompleted:
      case ActivityType.deliveryDelayed:
      case ActivityType.deliveryFailed:
        return 'Pengiriman';
      case ActivityType.userLogin:
      case ActivityType.userLogout:
      case ActivityType.systemMaintenance:
      case ActivityType.configurationChanged:
        return 'Sistem';
      case ActivityType.paymentReceived:
      case ActivityType.expenseRecorded:
      case ActivityType.budgetAlert:
        return 'Keuangan';
      case ActivityType.maintenanceScheduled:
      case ActivityType.maintenanceCompleted:
      case ActivityType.equipmentFailure:
        return 'Pemeliharaan';
      case ActivityType.reportGenerated:
      case ActivityType.approvalRequested:
      case ActivityType.approvalGranted:
      case ActivityType.approvalDenied:
      case ActivityType.notification:
      case ActivityType.alert:
        return 'Umum';
    }
  }
}

/// Extension methods for ActivitySeverity
extension ActivitySeverityExtension on ActivitySeverity {
  /// Human-readable name in Indonesian
  String get displayName {
    switch (this) {
      case ActivitySeverity.info:
        return 'Info';
      case ActivitySeverity.warning:
        return 'Peringatan';
      case ActivitySeverity.error:
        return 'Error';
      case ActivitySeverity.critical:
        return 'Kritis';
      case ActivitySeverity.success:
        return 'Berhasil';
    }
  }
}
