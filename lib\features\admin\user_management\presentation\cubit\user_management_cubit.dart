import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../app/services/export_service.dart';
import '../../../../../core/utils/app_error.dart';
import '../../../../../core/utils/connection_manager.dart';
import '../../../../../core/utils/debouncer.dart';
import '../../data/repositories/user_management_supabase_repository.dart';
import '../../domain/models/user_management.dart';
import '../../domain/repositories/user_management_repository.dart';
import 'user_management_state.dart';

/// Cubit untuk mengelola state User Management
class UserManagementCubit extends Cubit<UserManagementState> {
  final UserManagementRepository _repository;
  final ConnectionManager _connectionManager;
  final Logger _logger = Logger();

  // Internal state
  List<UserManagement> _allUsers = [];
  UserFilter _currentFilter = const UserFilter();
  Map<String, dynamic> _statistics = {};
  late final Debouncer _searchDebouncer;
  RealtimeChannel? _realtimeSubscription;
  StreamSubscription<bool>? _connectionSubscription;

  // Optimistic update tracking
  final Map<String, UserManagement> _optimisticUpdates = {};
  final Map<String, Timer> _rollbackTimers = {};

  UserManagementCubit(this._repository, this._connectionManager)
    : super(const UserManagementLoading()) {
    _searchDebouncer = Debouncer(delay: const Duration(milliseconds: 500));
    _initializeRealtimeSubscription();
    _initializeConnectionListener();
  }

  // =============================================
  // Core Data Loading
  // =============================================

  /// Load semua data pengguna
  Future<void> loadUsers() async {
    try {
      emit(const UserManagementLoading());
      _logger.i('UserManagementCubit: Loading users...');

      // Load users dan statistics secara paralel
      final futures = await Future.wait([
        _repository.getAllUsers(),
        _repository.getUserStatistics(),
      ]);

      _allUsers = futures[0] as List<UserManagement>;
      _statistics = futures[1] as Map<String, dynamic>;

      final filteredUsers = _filterUsers(_allUsers, _currentFilter);

      emit(
        UserManagementLoaded(
          users: _allUsers,
          filteredUsers: filteredUsers,
          currentFilter: _currentFilter,
          statistics: _statistics,
        ),
      );

      _logger.i(
        'UserManagementCubit: Loaded ${_allUsers.length} users successfully',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error loading users',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        UserManagementError(
          message: 'Gagal memuat data pengguna: ${e.toString()}',
          details: e.toString(),
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// Refresh data
  Future<void> refreshUsers() async {
    final currentState = state;
    if (currentState is UserManagementLoaded) {
      emit(currentState.copyWith(isRefreshing: true));
      await loadUsers();
    } else {
      await loadUsers();
    }
  }

  // =============================================
  // Search & Filter Operations
  // =============================================

  /// Melakukan pencarian dengan debouncing
  void searchUsers(String query) {
    _searchDebouncer.call(() => _applySearch(query));
  }

  void _applySearch(String query) {
    _logger.i('UserManagementCubit: Searching users with query: "$query"');

    final newFilter = _currentFilter.copyWith(
      searchQuery: query.isEmpty ? null : query,
    );
    _applyFilter(newFilter);
  }

  /// Menerapkan filter role
  void filterByRole(UserRole? role) {
    _logger.i(
      'UserManagementCubit: Filtering by role: ${role?.displayName ?? "All"}',
    );
    final newFilter = _currentFilter.copyWith(role: role);
    _applyFilter(newFilter);
  }

  /// Menerapkan filter status
  void filterByStatus(UserStatus? status) {
    _logger.i(
      'UserManagementCubit: Filtering by status: ${status?.displayName ?? "All"}',
    );
    final newFilter = _currentFilter.copyWith(status: status);
    _applyFilter(newFilter);
  }

  /// Menerapkan filter SPPG
  void filterBySppg(String? sppgId) {
    _logger.i('UserManagementCubit: Filtering by SPPG: ${sppgId ?? "All"}');
    final newFilter = _currentFilter.copyWith(sppgId: sppgId);
    _applyFilter(newFilter);
  }

  /// Menerapkan filter custom
  void applyCustomFilter(UserFilter filter) {
    _logger.i('UserManagementCubit: Applying custom filter');
    _applyFilter(filter);
  }

  /// Menghapus semua filter
  void clearFilters() {
    _logger.i('UserManagementCubit: Clearing all filters');
    _applyFilter(const UserFilter());
  }

  void _applyFilter(UserFilter filter) {
    _currentFilter = filter;

    final currentState = state;
    if (currentState is UserManagementLoaded) {
      final filteredUsers = _filterUsers(_allUsers, filter);

      emit(
        currentState.copyWith(
          filteredUsers: filteredUsers,
          currentFilter: filter,
        ),
      );
    }
  }

  /// Menerapkan filter ke list users
  List<UserManagement> _filterUsers(
    List<UserManagement> users,
    UserFilter filter,
  ) {
    if (filter.isEmpty) return users;
    return users.where((user) => filter.matches(user)).toList();
  }

  // =============================================
  // CRUD Operations with Optimistic Updates
  // =============================================

  /// Membuat user baru dengan optimistic updates
  Future<void> createUser(UserManagement user) async {
    if (_connectionManager.connectionState != ConnectionState.connected) {
      emit(
        const UserManagementCreateError(message: 'Tidak ada koneksi internet'),
      );
      return;
    }

    try {
      emit(const UserManagementCreating());
      _logger.i('UserManagementCubit: Creating user: ${user.nama}');

      // Validasi data
      final validationErrors = user.validateForCreation();
      if (!validationErrors.isValid) {
        emit(
          UserManagementCreateError(
            message: 'Data tidak valid',
            validationErrors: validationErrors.allErrors,
          ),
        );
        return;
      }

      // Cek keunikan email
      final isEmailUnique = await _repository.isEmailUnique(user.email);
      if (!isEmailUnique) {
        emit(
          const UserManagementCreateError(
            message: 'Email sudah digunakan',
            validationErrors: ['Email sudah terdaftar dalam sistem'],
          ),
        );
        return;
      }

      // Cek keunikan NIP jika ada
      if (user.nip != null && user.nip!.isNotEmpty) {
        final isNipUnique = await _repository.isNipUnique(user.nip!);
        if (!isNipUnique) {
          emit(
            const UserManagementCreateError(
              message: 'NIP sudah digunakan',
              validationErrors: ['NIP sudah terdaftar dalam sistem'],
            ),
          );
          return;
        }
      }

      // Optimistic update - add to local list immediately
      final tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';
      final optimisticUser = user.copyWith(id: tempId);
      _applyOptimisticCreate(optimisticUser);

      try {
        // Buat user
        final createdUser = await _retryOperation(
          () => _repository.createUser(user),
        );

        // Replace optimistic update with real data
        _removeOptimisticUpdate(tempId);
        _applyRealUpdate(createdUser);

        emit(UserManagementCreateSuccess(createdUser));

        // Kirim notifikasi welcome
        await _repository.sendWelcomeNotification(createdUser.id);

        _logger.i(
          'UserManagementCubit: User created successfully: ${createdUser.nama}',
        );
      } catch (e) {
        // Rollback optimistic update
        _rollbackOptimisticUpdate(tempId);
        rethrow;
      }
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error creating user',
        error: e,
        stackTrace: stackTrace,
      );
      final errorMessage = _getErrorMessage(e);

      emit(UserManagementCreateError(message: errorMessage));
    }
  }

  /// Update user
  Future<void> updateUser(UserManagement user) async {
    try {
      emit(UserManagementUpdating(user.id));
      _logger.i('UserManagementCubit: Updating user: ${user.nama}');

      // Validasi data
      final validationErrors = user.validateForUpdate();
      if (!validationErrors.isValid) {
        emit(
          UserManagementUpdateError(
            message: 'Data tidak valid: ${validationErrors.allErrors.join(', ')}',
            userId: user.id,
          ),
        );
        return;
      }

      // Cek keunikan email (exclude current user)
      final isEmailUnique = await _repository.isEmailUnique(
        user.email,
        excludeUserId: user.id,
      );
      if (!isEmailUnique) {
        emit(
          UserManagementUpdateError(
            message: 'Email sudah digunakan oleh user lain',
            userId: user.id,
          ),
        );
        return;
      }

      // Cek keunikan NIP jika ada (exclude current user)
      if (user.nip != null && user.nip!.isNotEmpty) {
        final isNipUnique = await _repository.isNipUnique(
          user.nip!,
          excludeUserId: user.id,
        );
        if (!isNipUnique) {
          emit(
            UserManagementUpdateError(
              message: 'NIP sudah digunakan oleh user lain',
              userId: user.id,
            ),
          );
          return;
        }
      }

      // Update user
      final updatedUser = await _repository.updateUser(user);

      // Update local state
      final index = _allUsers.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        _allUsers[index] = updatedUser;
        final filteredUsers = _filterUsers(_allUsers, _currentFilter);

        emit(UserManagementUpdateSuccess(updatedUser));

        // Kembali ke loaded state
        emit(
          UserManagementLoaded(
            users: _allUsers,
            filteredUsers: filteredUsers,
            currentFilter: _currentFilter,
            statistics: _statistics,
          ),
        );
      }

      _logger.i(
        'UserManagementCubit: User updated successfully: ${updatedUser.nama}',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error updating user',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        UserManagementUpdateError(
          message: 'Gagal memperbarui user: ${e.toString()}',
          userId: user.id,
        ),
      );
    }
  }

  /// Delete user (soft delete)
  Future<void> deleteUser(String userId) async {
    try {
      emit(UserManagementDeleting(userId));
      _logger.i('UserManagementCubit: Deleting user: $userId');

      await _repository.deleteUser(userId);

      // Update local state
      final index = _allUsers.indexWhere((u) => u.id == userId);
      if (index != -1) {
        _allUsers[index] = _allUsers[index].copyWith(
          status: UserStatus.inactive,
        );
        final filteredUsers = _filterUsers(_allUsers, _currentFilter);

        // Refresh statistics
        _statistics = await _repository.getUserStatistics();

        emit(UserManagementDeleteSuccess(userId));

        // Kembali ke loaded state
        emit(
          UserManagementLoaded(
            users: _allUsers,
            filteredUsers: filteredUsers,
            currentFilter: _currentFilter,
            statistics: _statistics,
          ),
        );
      }

      _logger.i('UserManagementCubit: User deleted successfully: $userId');
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error deleting user',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        UserManagementDeleteError(
          message: 'Gagal menghapus user: ${e.toString()}',
          userId: userId,
        ),
      );
    }
  }

  // =============================================
  // Status Management
  // =============================================

  /// Mengubah status user
  Future<void> updateUserStatus(String userId, UserStatus newStatus) async {
    try {
      _logger.i(
        'UserManagementCubit: Updating user $userId status to: ${newStatus.displayName}',
      );

      await _repository.updateUserStatus(userId, newStatus);

      // Update local state
      final index = _allUsers.indexWhere((u) => u.id == userId);
      if (index != -1) {
        _allUsers[index] = _allUsers[index].copyWith(status: newStatus);
        await _refreshCurrentState();
      }

      _logger.i('UserManagementCubit: User status updated successfully');
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error updating user status',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        UserManagementError(
          message: 'Gagal mengubah status user: ${e.toString()}',
        ),
      );
    }
  }

  /// Suspend user
  Future<void> suspendUser(
    String userId, {
    DateTime? until,
    String? reason,
  }) async {
    try {
      _logger.i('UserManagementCubit: Suspending user: $userId');

      await _repository.suspendUser(userId, until: until, reason: reason);

      // Update local state
      final index = _allUsers.indexWhere((u) => u.id == userId);
      if (index != -1) {
        _allUsers[index] = _allUsers[index].copyWith(
          status: UserStatus.suspended,
          suspendedUntil: until,
          notes: reason,
        );
        await _refreshCurrentState();
      }

      // Kirim notifikasi suspension
      if (reason != null) {
        await _repository.sendSuspensionNotification(userId, reason);
      }

      _logger.i('UserManagementCubit: User suspended successfully');
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error suspending user',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        UserManagementError(
          message: 'Gagal menangguhkan user: ${e.toString()}',
        ),
      );
    }
  }

  /// Unsuspend user
  Future<void> unsuspendUser(String userId) async {
    try {
      _logger.i('UserManagementCubit: Unsuspending user: $userId');

      await _repository.unsuspendUser(userId);

      // Update local state
      final index = _allUsers.indexWhere((u) => u.id == userId);
      if (index != -1) {
        _allUsers[index] = _allUsers[index].copyWith(
          status: UserStatus.active,
          suspendedUntil: null,
        );
        await _refreshCurrentState();
      }

      // Kirim notifikasi reactivation
      await _repository.sendReactivationNotification(userId);

      _logger.i('UserManagementCubit: User unsuspended successfully');
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error unsuspending user',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        UserManagementError(
          message: 'Gagal mengangkat penangguhan user: ${e.toString()}',
        ),
      );
    }
  }

  // =============================================
  // Bulk Operations
  // =============================================

  /// Update status multiple users
  Future<void> updateMultipleUserStatus(
    List<String> userIds,
    UserStatus status,
  ) async {
    try {
      emit(
        UserManagementBulkProcessing(
          operation: 'update_status',
          total: userIds.length,
          processed: 0,
        ),
      );

      _logger.i(
        'UserManagementCubit: Updating status for ${userIds.length} users',
      );

      await _repository.updateMultipleUserStatus(userIds, status);

      // Update local state
      for (final userId in userIds) {
        final index = _allUsers.indexWhere((u) => u.id == userId);
        if (index != -1) {
          _allUsers[index] = _allUsers[index].copyWith(status: status);
        }
      }

      emit(
        UserManagementBulkSuccess(
          operation: 'update_status',
          count: userIds.length,
          message: 'Status ${userIds.length} pengguna berhasil diperbarui',
        ),
      );

      await _refreshCurrentState();
      _logger.i('UserManagementCubit: Bulk status update completed');
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error in bulk status update',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        UserManagementBulkError(
          operation: 'update_status',
          message: 'Gagal memperbarui status pengguna: ${e.toString()}',
        ),
      );
    }
  }

  /// Delete multiple users
  Future<void> deleteMultipleUsers(List<String> userIds) async {
    try {
      emit(
        UserManagementBulkProcessing(
          operation: 'delete',
          total: userIds.length,
          processed: 0,
        ),
      );

      _logger.i('UserManagementCubit: Deleting ${userIds.length} users');

      await _repository.deleteMultipleUsers(userIds);

      // Update local state
      for (final userId in userIds) {
        final index = _allUsers.indexWhere((u) => u.id == userId);
        if (index != -1) {
          _allUsers[index] = _allUsers[index].copyWith(
            status: UserStatus.inactive,
          );
        }
      }

      emit(
        UserManagementBulkSuccess(
          operation: 'delete',
          count: userIds.length,
          message: '${userIds.length} pengguna berhasil dihapus',
        ),
      );

      await _refreshCurrentState();
      _logger.i('UserManagementCubit: Bulk delete completed');
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error in bulk delete',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        UserManagementBulkError(
          operation: 'delete',
          message: 'Gagal menghapus pengguna: ${e.toString()}',
        ),
      );
    }
  }

  // =============================================
  // Export Operations
  // =============================================

  /// Export users to CSV
  Future<void> exportToCSV({UserFilter? filter}) async {
    try {
      emit(const UserManagementExporting('CSV'));
      _logger.i('UserManagementCubit: Exporting users to CSV');

      // Get filtered users
      final filterToUse = filter ?? _currentFilter;
      final usersToExport = _filterUsers(_allUsers, filterToUse);

      if (usersToExport.isEmpty) {
        emit(
          const UserManagementExportError(
            format: 'CSV',
            message: 'Tidak ada data untuk diekspor',
          ),
        );
        return;
      }

      // Generate filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename = 'users_export_$timestamp';

      // Use export service
      final exportService = ExportServiceImpl();
      final result = await exportService.exportUsers(
        users: usersToExport,
        filename: filename,
        format: ExportFormat.csv,
        onProgress: (progress) {
          // Could emit progress updates here if needed
        },
      );

      if (result.success) {
        emit(
          UserManagementExportSuccess(
            format: 'CSV',
            filePath: result.filePath!,
            recordCount: result.recordCount!,
          ),
        );

        _logger.i('UserManagementCubit: CSV export completed successfully');
      } else {
        emit(
          UserManagementExportError(
            format: 'CSV',
            message: result.errorMessage!,
          ),
        );
      }
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error exporting to CSV',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        UserManagementExportError(
          format: 'CSV',
          message: 'Gagal mengekspor ke CSV: ${e.toString()}',
        ),
      );

      // Error notification will be handled by the UI layer
    }
  }

  /// Export users to Excel
  Future<void> exportToExcel({UserFilter? filter}) async {
    try {
      emit(const UserManagementExporting('Excel'));
      _logger.i('UserManagementCubit: Exporting users to Excel');

      // Get filtered users
      final filterToUse = filter ?? _currentFilter;
      final usersToExport = _filterUsers(_allUsers, filterToUse);

      if (usersToExport.isEmpty) {
        emit(
          const UserManagementExportError(
            format: 'Excel',
            message: 'Tidak ada data untuk diekspor',
          ),
        );
        return;
      }

      // Generate filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename = 'users_export_$timestamp';

      // Use export service
      final exportService = ExportServiceImpl();
      final result = await exportService.exportUsers(
        users: usersToExport,
        filename: filename,
        format: ExportFormat.excel,
        onProgress: (progress) {
          // Could emit progress updates here if needed
        },
      );

      if (result.success) {
        emit(
          UserManagementExportSuccess(
            format: 'Excel',
            filePath: result.filePath!,
            recordCount: result.recordCount!,
          ),
        );

        _logger.i('UserManagementCubit: Excel export completed successfully');
      } else {
        emit(
          UserManagementExportError(
            format: 'Excel',
            message: result.errorMessage!,
          ),
        );
      }
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error exporting to Excel',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        UserManagementExportError(
          format: 'Excel',
          message: 'Gagal mengekspor ke Excel: ${e.toString()}',
        ),
      );

      // Error notification will be handled by the UI layer
    }
  }

  // =============================================
  // Helper Methods
  // =============================================

  /// Refresh current state dengan data terbaru
  Future<void> _refreshCurrentState() async {
    final currentState = state;
    if (currentState is UserManagementLoaded) {
      final filteredUsers = _filterUsers(_allUsers, _currentFilter);
      _statistics = await _repository.getUserStatistics();

      emit(
        currentState.copyWith(
          users: _allUsers,
          filteredUsers: filteredUsers,
          statistics: _statistics,
        ),
      );
    }
  }

  /// Get user by ID
  UserManagement? getUserById(String id) {
    try {
      return _allUsers.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get current filter
  UserFilter get currentFilter => _currentFilter;

  /// Get current statistics
  Map<String, dynamic> get statistics => _statistics;

  // =============================================
  // Password Reset Operations
  // =============================================

  /// Reset password for user
  Future<void> resetPassword(String userId) async {
    if (_connectionManager.connectionState != ConnectionState.connected) {
      return;
    }

    try {
      _logger.i('UserManagementCubit: Resetting password for user: $userId');

      // Get user email first
      final user = getUserById(userId);
      if (user == null) {
        throw Exception('User not found');
      }

      await _retryOperation(
        () => (_repository as UserManagementSupabaseRepository).resetPassword(
          user.email,
        ),
      );

      _logger.i(
        'UserManagementCubit: Password reset successful for user: $userId',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'UserManagementCubit: Error resetting password',
        error: e,
        stackTrace: stackTrace,
      );
      // Error message handled by logging above
    }
  }

  // =============================================
  // Optimistic Update Helpers
  // =============================================

  /// Apply optimistic create update
  void _applyOptimisticCreate(UserManagement user) {
    _optimisticUpdates[user.id] = user;
    _allUsers.add(user);
    _updateCurrentState();
  }

  /// Apply optimistic update
  void _applyOptimisticUpdate(UserManagement user) { // ignore: unused_element
    _optimisticUpdates[user.id] = user;
    final index = _allUsers.indexWhere((u) => u.id == user.id);
    if (index != -1) {
      _allUsers[index] = user;
      _updateCurrentState();
    }
  }

  /// Apply optimistic delete
  void _applyOptimisticDelete(String id) { // ignore: unused_element
    final user = _allUsers.firstWhere((u) => u.id == id);
    _optimisticUpdates[id] = user;
    _allUsers.removeWhere((u) => u.id == id);
    _updateCurrentState();
  }

  /// Apply real update from server
  void _applyRealUpdate(UserManagement user) {
    _removeOptimisticUpdate(user.id);
    final index = _allUsers.indexWhere((u) => u.id == user.id);
    if (index != -1) {
      _allUsers[index] = user;
    } else {
      _allUsers.add(user);
    }
    _updateCurrentState();
  }

  /// Remove optimistic update
  void _removeOptimisticUpdate(String id) {
    _optimisticUpdates.remove(id);
  }

  /// Schedule rollback for optimistic update
  void _scheduleRollback(String id, UserManagement originalUser) { // ignore: unused_element
    _rollbackTimers[id]?.cancel();
    _rollbackTimers[id] = Timer(const Duration(seconds: 10), () {
      _rollbackOptimisticUpdate(id, originalUser);
    });
  }

  /// Cancel scheduled rollback
  void _cancelRollback(String id) {
    _rollbackTimers[id]?.cancel();
    _rollbackTimers.remove(id);
  }

  /// Rollback optimistic update
  void _rollbackOptimisticUpdate(String id, [UserManagement? originalUser]) {
    _cancelRollback(id);
    _removeOptimisticUpdate(id);

    if (originalUser != null) {
      // Restore original data
      final index = _allUsers.indexWhere((u) => u.id == id);
      if (index != -1) {
        _allUsers[index] = originalUser;
      } else {
        _allUsers.add(originalUser);
      }
    } else {
      // Remove from list (for create operations)
      _allUsers.removeWhere((u) => u.id == id);
    }

    _updateCurrentState();
  }

  /// Update current state with new data
  void _updateCurrentState() {
    final currentState = state;
    if (currentState is UserManagementLoaded) {
      final filteredUsers = _filterUsers(_allUsers, _currentFilter);

      emit(
        currentState.copyWith(users: _allUsers, filteredUsers: filteredUsers),
      );
    } else if (_allUsers.isNotEmpty) {
      emit(
        UserManagementLoaded(
          users: _allUsers,
          filteredUsers: _filterUsers(_allUsers, _currentFilter),
          currentFilter: _currentFilter,
          statistics: _statistics,
        ),
      );
    }
  }

  // =============================================
  // Real-time Synchronization
  // =============================================

  /// Initialize real-time subscription
  void _initializeRealtimeSubscription() {
    if (_repository is UserManagementSupabaseRepository) {
      final supabaseRepo = _repository as UserManagementSupabaseRepository;

      _realtimeSubscription = supabaseRepo.subscribeToUserChanges(
        onInsert: _handleRealtimeInsert,
        onUpdate: _handleRealtimeUpdate,
        onDelete: _handleRealtimeDelete,
      );

      _logger.d('Real-time subscription initialized for users');
    }
  }

  /// Handle real-time insert
  void _handleRealtimeInsert(UserManagement user) {
    _logger.d('Real-time insert: ${user.id}');

    // Don't add if it's our own optimistic update
    if (_optimisticUpdates.containsKey(user.id)) return;

    // Add to list if not already present
    if (!_allUsers.any((u) => u.id == user.id)) {
      _allUsers.add(user);
      _updateCurrentState();
    }
  }

  /// Handle real-time update
  void _handleRealtimeUpdate(UserManagement user) {
    _logger.d('Real-time update: ${user.id}');

    // Don't update if it's our own optimistic update
    if (_optimisticUpdates.containsKey(user.id)) return;

    final index = _allUsers.indexWhere((u) => u.id == user.id);
    if (index != -1) {
      _allUsers[index] = user;
      _updateCurrentState();
    }
  }

  /// Handle real-time delete
  void _handleRealtimeDelete(String id) {
    _logger.d('Real-time delete: $id');

    // Don't delete if it's our own optimistic update
    if (_optimisticUpdates.containsKey(id)) return;

    final userIndex = _allUsers.indexWhere((u) => u.id == id);
    if (userIndex != -1) {
      // final user = _allUsers[userIndex]; // User reference not needed
      _allUsers.removeAt(userIndex);
      _updateCurrentState();
    }
  }

  // =============================================
  // Connection Management
  // =============================================

  /// Initialize connection listener
  void _initializeConnectionListener() {
    _connectionSubscription = _connectionManager.networkStateStream.listen((
      isConnected,
    ) {
      if (isConnected) {
        _logger.d('Connection restored, refreshing user data');
        refreshUsers();
      } else {
        _logger.d('Connection lost');
      }
    });
  }

  // =============================================
  // Error Handling
  // =============================================

  /// Get user-friendly error message
  String _getErrorMessage(dynamic error) {
    if (error is AppError) {
      return error.message;
    }

    if (error is PostgrestException) {
      switch (error.code) {
        case '23505':
          return 'Data sudah ada, silakan gunakan email atau NIP yang berbeda';
        case '23503':
          return 'Tidak dapat menghapus data karena masih digunakan';
        case '42501':
          return 'Anda tidak memiliki izin untuk melakukan aksi ini';
        case 'PGRST116':
          return 'Data tidak ditemukan';
        default:
          return error.message;
      }
    }

    if (error is AuthException) {
      return 'Sesi Anda telah berakhir, silakan login kembali';
    }

    if (error.toString().contains('SocketException') ||
        error.toString().contains('TimeoutException')) {
      return 'Tidak ada koneksi internet';
    }

    return 'Terjadi kesalahan yang tidak terduga';
  }

  /// Retry operation with exponential backoff
  Future<T> _retryOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
  }) async {
    int retryCount = 0;
    Duration delay = initialDelay;

    while (retryCount < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        retryCount++;

        if (retryCount >= maxRetries) {
          rethrow;
        }

        _logger.w(
          'Operation failed, retrying in ${delay.inSeconds}s (attempt $retryCount/$maxRetries)',
        );
        await Future.delayed(delay);
        delay *= 2; // Exponential backoff
      }
    }

    throw Exception('Max retries exceeded');
  }

  @override
  Future<void> close() {
    _searchDebouncer.dispose();
    _realtimeSubscription?.unsubscribe();
    _connectionSubscription?.cancel();

    // Cancel all rollback timers
    for (final timer in _rollbackTimers.values) {
      timer.cancel();
    }
    _rollbackTimers.clear();

    return super.close();
  }
}
